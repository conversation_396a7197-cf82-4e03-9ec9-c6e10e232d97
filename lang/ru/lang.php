<?php

return [
    'plugin' => [
        'name' => 'Загрузчик',
        'desc' => 'Инструменты для загрузки файлов и изображений'
    ],
    'component' => [
        'file_uploader' => 'Загрузчик файлов',
        'file_uploader_desc' => 'Загрузить файл',
        'image_uploader' => 'Загрузчик изображений',
        'image_uploader_desc' => 'Загрузка изображений с предпросмотром'
    ],
    'prop' => [
        'placeholder' => 'Текст заполнителя',
        'placeholder_file_desc' => 'Формулировка для отображения, когда файл не загружен',
        'placeholder_img_desc' => 'Формулировка для отображения, когда изображение не загружено',
        'maxSize' => 'Максимальный размер файла (Мб)',
        'maxSize_desc' => 'Максимальный размер файла, который можно загрузить в мегабайтах.',
        'fileTypes' => 'Поддерживаемые типы файлов',
        'fileTypes_desc' => 'Расширения файлов, разделенные запятыми (,) или звездочка (*), чтобы разрешить все типы.',
        'imageWidth' => 'Ширина превью изображения',
        'imageWidth_desc' => 'Введите значение в пикселях, например: 100',
        'imageHeight' => 'Высота превью изображения',
        'imageHeight_desc' => 'Введите значение в пикселях, например: 100',
        'imageMode' => 'Мод превью изображения',
        'imageMode_desc' => 'Thumb mode для превью, например: exact, portrait, landscape, auto или crop',
        // 'previewFluid' => 'Fluid preview',
        // 'previewFluid_desc' => 'Изображение должно расшириться, чтобы соответствовать размеру его контейнера',
        'deferredBinding' => 'Использовать отложенное связывание',
        'deferredBinding_desc' => 'Если этот флажок установлен, соответствующая модель должна быть сохранена, чтобы загрузка была привязана.',
    ],
    'are_you_sure' => 'Вы уверены?'
];
