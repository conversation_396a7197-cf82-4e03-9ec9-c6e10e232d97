View the changelog on the [October CMS website](https://octobercms.com/changelog)
v3.0.4:
- Deactivate call to openweathermap (397)
- Delete jobs from the queue for tasks already soft-deleted (425)
- Javascript error in the console (410)
- Missing images in dossier de vente (434)
- Cookie and privacy policy pages (432)
v3.0.5:
- Ability to see the task listing in the mobile version (ipad and laptop) (448)
- Put the confidentiality and cookie information pages in the footer (437)
- Logo update - SMG (438)
- Remove deactivated plugins from the composer
v3.0.6
- Remove crazyEgg + facebook (483)
v3.0.7
- Remove chatra chatbox, webhook and column chatra_id in table users (484)
v3.0.8
- KIIZ-495 Modifier l'adresse Kiiz (exporter le csv de traduction)
- KIIZ-502 Add validator in backend field-date
v3.0.9
- KIIZ-452 Refaire les rapports hebdomadaires en supprimant les graphiques actuels
- KIIZ-454 Faire en sorte que les propriétaires puissent nous donner leur disponibilités sans pouvoir voir celles qui seront affichées dans l'agenda général (Importer les traductions)
- KIIZ-445 Renommer l'onglet Clients en Contacts
- KIIZ-439 Lorsqu'une prise de mandat est confirmée, le nom du user doit apparaitre dans l'email de confirmation et de rappel (Importer les traductions)
V3.1.0
- KIIZ-480 Refonte du site
v3.1.1
- KIIZ-597 Remplacer le PDF des conseils photos
- IIZ-591 Signature d'email - Changer le logo et les images des réseaux sociaux
v3.1.2
- KIIZ-593 Suivi des bugs liés au déploiement de la refonte
- KIIZ-585 Refonte page courtièregit status
- KIIZ-594 SEO - Post delivery points to fix
- KIIZ-556 SEO - Implement Structured Data
- KIIZ-485 Sitemap
v3.1.3
- KIIZ-455 Dans l'email et le sms seance photo au photographe, afficher les⁠ coordonnées du locataire (Importer les traductions)
- KIIZ-456 Dans l'email et le sms seance photo au photographe, afficher l'ID de la fiche propriété
- KIIZ-441 Envoi d'un sms de rappel pour le user pour les prises de mandat (comme pour les visites) (Importer les traductions)
- KIIZ-443 Pouvoir modifier les templates de contrats et conditions générales dans le backoffice
- KIIZ-457 Dans l'email qui envoie le résultat de l'estimation gratuite, expliquer brièvement ce qu'est une estimation gratuite et renvoyer vers notre site pour des explications plus détaillées. (Importer les traductions)
- KIIZ-524 Mise à jour des roles et des comptes
- KIIZ-447 Mettre un place un système d'envoi automatique d'email aux personnes ayant demandé le dossier de vente lorsqu'il y a une baisse de prix
- KIIZ-449 SMS de relance automatique pour les demandes d'estimations avec le nom de l'utilisateur qui demande la relance (Ajouter la traduction et importer)
- KIIZ-444 Mettre en place un système de relance aux personnes n'ayant pas donné suite aux demandes de dossiers de vente (Configuration email)
- KIIZ-566 Preview Email
v3.1.3.1
- KIIZ-618 Ajouter Jura Bernois dans l'enumeration sub_region
- KIIZ-617 Envoi automatique d'email q'aux personnes ayant demandé le dossier de vente lorsqu'il y a une baisse de prix
v3.1.4
- KIIZ-588 Revoir bloc Mon actualité
- KIIZ-458 Automatiser la production des factures (Partial implementation for next release)
- KIIZ-596 Refonte du dossier de vente
- KIIZ-608 refaire l’email que reçoivent ceux qui téléchargent le Guide du propriétaire.
- KIIZ-610 Revoir le SVG des flèches des sliders et l'espacement de l'icône pour les prestations sur mobile
- KIIZ-609 Afficher des guillemets français dans la FAQ
- KIIZ-598 Menu des Articles par Catégorie
- KIIZ-613 Le logo de Kiiz des emails est un peu flou
- KIIZ-612 Telechargement du guide - Ajustement de la taille du message et du titre du pdf
- KIIZ-614 Facture - Ajouter en cc dans l'email de facture
- KIIZ-611 Dossier de vente - Revoir l'affichage des blocs
- KIIZ-615 Ne pas relancer le dossier de vente sous certaines conditions
- KIIZ-601 SEO - Make headings look bolder (css)
- KIIZ-620 Permettre une prise de mandat pour une estimation en statut "A relancer"
v3.1.4.1:
- KIIZ-581 Remplacer le numéro de téléphone fixe par le numéro mobile sur homegate et immoscout
v3.1.4.2:
- KIIZ-462 Portails immobiliers (assign-canton-to-collaborator)
- Lookerstudio modification du comptage du portefolio
v3.1.5:
- KIIZ-621 Remove immoscout calls
- KIIZ-622 Réduction de la taille de la police du contenu des emails
- KIIZ-616 Calendrier des visites
- KIIZ-623 Ajouter les factures a la release v3.1.5
- KIIZ-626 Ajustement des factures
- KIIZ-607 Signature contrat - relance - creation tache
- KIIZ-628 Sauvegarder la date de la facture
- KIIZ-631 Remove sarah account
- KIIZ-633 Teaser des articles : Le nombre d'affichage ne correspond pas au parémétrage
v3.1.5.1:
- KIIZ-635 Affichage de l'onglet facture
v3.1.6:
- KIIZ-627 Suppression de la tache "appeler le client pour finaliser l'estimation" pour Valuation only
- Add ext-ftp to local dockerfile
- KIIZ-638 Enlever l'email de la conseillère sur la fiche propriété
- KIIZ-639 Envoyer le message de la ceo + 24 heures pour les valuation only / Supprimer le message de la ceo pour les full service
- KIIZ-637 Réseaux sociaux. Creation d'un flux RSS
v3.1.7:
- KIIZ-440 Prévoir la possibilité d'éditer tous les emails
- KIIZ-647 Ajouter un filtre par defaut qui affiche les proprietes du courtier sur la page propriétés. Et l'administrateur voit toutes les proprietes.
- KIIZ-648 Revoir les filtres du calendrier
- KIIZ-649 Ajouter un filtre par défaut qui affiche uniquement les estimations du courtier sur la page des estimations . L'administrateur pourra voir toutes les estimations.
- KIIZ-451 Enlever ou déplacer la colonne de rating
- KIIZ-646 Supprimer l'adresse personnaliser sur le dashboard <NAME_EMAIL>
v3.1.8:
- KIIZ-652 Les emails ne doivent pas être archivés automatiquement et doivent être assignés aux collaborateurs.
- KIIZ-450 Création deux tâches automatique
- KIIZ-659 Add a lien redirect to google profile
- KIIZ-657 Créer un backoffice pour changer l'image de droit + nommée Dashboard client
- KIIZ-654 / KIIZ-662 Filtre estimations : filtré par defaut sur full service
- KIIZ-661 Confirmation de Mandat - Ajouter Marc en cc dans l'email
- KIIZ-658 FAQ propriétaire remplacer par envoyer par enregistrer
v3.1.9:
- KIIZ-665 Changement de numero de telephone
- KIIZ-655 Fiche de prise de rendez-vous : ajouter le texte suivant
- KIIZ-663 landing page special guest
- KIIZ-660 Banner homepage Kiiz info
v3.1.10:
- KIIZ-667 Ajouter une page de gestion des factures
- KIIZ-656 Afficher le numero du collaborateur assigné dans tous les emails envoyés au client
- KIIZ-675 Remove "Vendu" (Sold) from the default selected option in the "État" filter of the property list.
- KIIZ-676 Remove "Offre acceptée" from the default selected option in the "État" filter of the estimation list.
- KIIZ-677 Add the estimation or property ID after the email subject in the format: (Ref: 1234)
- KIIZ-680 Change Action Label from "Contrat envoyé" to "Envoyer contrat"
- KIIZ-684 Vidéo du spot sur la page Cuche - Ne démarre pas
v3.1.11:
- KIIZ-681 Fix Date Display and Validate Price History
- KIIZ-679 Remove Estimation Certificate Implementation and Related Elements
- KIIZ-682 Archive some emails in frontapp
- KIIZ-403 Importer le robox.txt de prod dans le git
- KIIZ-653 Creation du formulaire de feelback
- KIIZ-683 Update recipients for email sent to "Responsable des Ventes"
- KIIZ-685 Enlever l'envoi de la tache automatique des rapport destiner au client
- KIIZ-687 Supprimer le sms de rappel destiner au collaborateur sauf au photographe
- KIIZ-688 Estimation export menu not marked when selected
v3.1.12:
- KIIZ-686 tache automatique à 2 semaine pour faire le point entretien telephonique
- KIIZ-699 Dépublier la propriété des portails immobiliers après Actions >Rdv chez le notaire
- KIIZ-666 flux RSS
- KIIZ-431 ne pas index /app/storage/
- KIIZ-674 Mauvais logo sur WhatsApp (et les autres réseaux sociaux)
- KIIZ-550 Fixed error when not contain mail sender
- KIIZ-697 Amélioration fiches acheteurs/vendeurs
- KIIZ-687 Reestablish the notifications to collaborators for Visits and Mandates
v3.1.13:
- KIIZ-707 email Confirmation de mandat : ajouter les liens dashboard
- KIIZ-709 email : seance photo deplacée: ajouter ancienne date
- KIIZ-710 Email : publication de l'annonce sur les portails: Ajouter le lien dashboard
- KIIZ-712 email Votre visite est confirmée (à l'acheteur potentiel) : ajouter le lien Documents publics relatifs à la propriété
v3.1.14:
- KIIZ-713 : Suppression emails de rappel automatique qui partent, lorsqu'il n'y a plus de disponibilités ou pas assez, pour relancer les propriétaires
- KIIZ-715 : Remove the button to the email 'La précision a un prix' from the estimations
- KIIZ-716 : Rename "Vérification des données" to "Relance de dernier recours"
- KIIZ-698 Error when creating an estimate from the backoffice
- KIIZ-552 kiiz - erreur en prod - ErrorException: Attempt to read property "email" on null in
- KIIZ-634 Manage 404 errors and redirectsn
- KIIZ-714 : kiiz : bug autocomplete google map homepage (renew key)
v3.1.15:
- KIIZ-664 : Migrate Fahrländer API to REST before 2026
- KIIZ-728 : supprimer le sms relance estimation
- KIIZ-727 : Enlever email Le moment de vendre votre bien est-il venu?
v3.1.16:
- KIIZ-729 : Fiche Etablissement d'un acte de vente : Ajouter les champs contenu sur la fiche vendeur et client
- KIIZ-732 : Notification des feedbacks par email
v3.1.17:
- KIIZ-548 : Add site selector and German language translation to the site
- KIIZ-720 : Prise de mandat : Remove available before , during after the meeting
- KIIZ-742 : Make the Canton translation as 'locale' to avoid unwanted copies between sites
- KIIZ-744 : Filter cantons by language on the Property widget
- KIIZ-751 : Filter cantons by language on the Property page
- KIIZ-762 : Add site selector with design in the footer
- KIIZ-774 : Allow cookie consent banner on German
- KIIZ-800 : Fix unsubscribe email translation
- KIIZ-805 : Translate the automatically generated general conditions documents
- KIIZ-811 : Email of Guide is not translate DE
- KIIZ-777 : Management of bilingual cantons
- KIIZ-824 : German seller receives the email in French
- KIIZ-826 : Feedback : probleme de droits
- KIIZ-823 : Email rappel de visite : ajouter un lien pour bouton offre + feedback
- KIIZ-783 : Utilisation de API price huble lors la premiere estimations
- KIIZ-804 : Dashboard : offre acceptée and nouvelle offre
- KIIZ-830 : PROD: the browser with German configuration redirects to the DE site
v3.1.18:
- KIIZ-574 : API kiiz-wuest
