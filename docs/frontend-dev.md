# Front End Javascript/CSS Compilation

You need to be in the front end assets directory in order to run any command :
```
cd themes/kiiz
```

If it's the first time you have to install all the npm dependencies :
```
npm install
```

## Building for production or development install
```
npm run build
```

## Working in development
When you want to edit JS or CSS files, you should run the below command ([see Useful bash aliases](dev-quick-start.md)) so the code can be autogenerated
```
gulp-frontend
```

The kiiz front end is located under the `themes/kiiz` directory.  The `themes/kiiz/assets` directory contains the
compiled images, CSS and JS files. You should never modify this files as changes will be lost with the next compilation.

The HTML files are located under `themes/kiiz/pages` and `themes/kiiz/partials` with Twig templates.

The CSS and JS files are located under `themes/kiiz/scr`.
