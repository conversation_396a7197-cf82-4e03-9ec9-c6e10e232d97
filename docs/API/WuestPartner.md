# warning  20.01.24 not used in V3
Introduction
============
Price Hubble is a real estate estimator which provides a REST API to gather information from appartments and houses.

## API Doumentation
Make sure you read all the documentation of Wüest Partner. It's located in Dropbox:
```
kiiz Dev/API/W&P/Documentation/api-doc-short/api-doc-short.html
```
### Endpoints
* test: <https://ws.wuestdimensions.com/ws> (see preprod authentification token in the config)
* prod: <https://ws.wuestdimensions.com/ws>

### Ressources
`calculator/valuation`: Used to perform the valuation.

The microlocation is automatically calculated given the address. Only the `calculator/valuation` resource is needed
to perform a valuation.
