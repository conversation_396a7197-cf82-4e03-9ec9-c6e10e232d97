# The big picture
<PERSON><PERSON> uses 5 estimation institutes to overcome with the most accurate valuation in the market.
- [CIFI](API/CIFI.md) disabled
- [Fahrlander SOAP](API/Fahrlander_soap.md) disabled
- [Fahrlander REST](API/Fahrlander_rest.md)
- [Price Hubble](API/PriceHubble.md)
- [SREI](API/SREI.md) disabled
- [Wüest Partner](API/WuestPartner.md) disabled

## Introduction
When a customer first comes to valuate his property, he fills a quick form (~10 fields) that gives him a price range.
Then he’s contacted by phone from one valuation expert to explain our pricing and conditions. If the customer is interested
in going forward with an accurate valuation, our expert performs the valuation (via the backend) that will give the kiiz price.
That will be a starting point to enter the negotiation with the customer to sell his property.

### Quick Valuation
A quick valuation is a [short form](../typeform.md) composed of around 10 fields that gives the customer a price range of his property.
Usually the customer accesses either the homepage, or an [Instapage](../instapage.md) landing page. When the form is submitted, we perform a valuation using only <PERSON><PERSON><PERSON><PERSON> (backfall with <PERSON><PERSON><PERSON> Partner).
### Accurate valuation
Once the customer is convinced about getting an accurate valuation, our expert goes on the backend and performs an accurate
valuation that will make API requests to the 5 valuation institutes, and give the kiiz price.
### kiiz price
Each one of the 5 valuation institutes have weaknesses and strengths. The Prof. Thalmann (EPFL) studied over 10k valuations
provided from Fahrlander to create a ponderation for each on of the institutes. The result of this is the kiiz price
that's used to provide valuation Cetificates and starting selling price with the customer.
