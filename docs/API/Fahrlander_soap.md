Introduction (DEPRECATED)
============
Fahrland<PERSON> is a real estate estimator which provides a SOAP API to gather information from appartments and houses.
In order establish a connection with their API, the application must provide a Certificate to ensure the security and
authenticity of the connection.

## API
You can find the SOAP API definition [here](/API/Fahrlander/20180219_FPRE_IMBAS_WEBS_Doku.de.en.pdf ':ignore').
The enabled modules are
* Geocode
* EFH (Evaluation Model Single Family): Method for determining the market value of single-family homes.
* EWG

## CSR Certificate Request (deprecated)
A CSR certificate is required to ensure the security and authenticity of each API call. Below the procedure of Certificate
creation.

1. Create a new CSR certificate from your machine
    ```bash
    openssl req -new -newkey rsa:2048 -keyout fahrlander.key -out fahrlander.csr
    ```
1. Answer the questions with the following information
    * Enter the passphare and store it securely. Don't put special characters on your passphrase because it won't work
    * Country Name: CH
    * State or Province Name: Vaud
    * Locality Name: Lausanne
    * Organization Name: Kiiz SA
    * Organizational Unit Name: IT
    * Common Name (e.g. server FQDN or YOUR name): www.kiiz.ch
    * Email Address: <EMAIL>
    * A challenge password: (empty)
    * An optional company name: (empty)
1. Check the content of the `fahrlander.csr`. It should look like this:
    ```
    -----BEGIN CERTIFICATE REQUEST-----
    MIIC4TCCAckCAQAwgZsxCzAJBgNVBAYTAkNIMQ0wCwYDVQQIDARWYXVkMREwDwYD
    ...
    XlYNWRGOFCtc2KN/247QYn6ayWo6
    -----END CERTIFICATE REQUEST-----
    ```
    1. Go to <https://webservice.fpre.ch/certsrv/certrqxt.asp> and copy/paste the content of the `fahrlander.csr` file, then submit the form.
    1. Write the confirmation number somewhere to keep track of the requests.
    1. Once you've received the certificate, as `.cer` file, you have to convert it to a `.pem` file
    ```bash
    openssl x509 -inform der -in certificate.cer -out certificate.pem
    ```
    1. You then need to concatenate your private key with the `certificate.pem`
    ```bash
    cat fahrlander.key certificate.pem > fahrlander.pem
    ```
    1. Then in the `config/kiiz/estimator/config.php` and `config/kiiz/estimator/prod/config.php` you have to update the
    certificate with the new passphrase.

The official Certificate generation is located [here](/API/Fahrlander/20171013_FPRE_IMBAS_WEBS_Zertifikate.de.en.pdf ':target=_blank:ignore').
