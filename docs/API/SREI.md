# warning  20.01.24 not used in V3

SREI
============
SREI (Swiss Real Estate Institute) is the least reliable institute as their core technology is really old.
They've built an API specially for kiiz but its behaviour is not the one we usually expect for this kind of API. We’ll through that point below.
## API
You can find the API documentation [here](https://api.meta-sys.ch/docs). There's no test environment.
* simulation_create[POST]: `https://api.meta-sys.ch/v1/hedo/simulations`
Creates and starts a new simulation task if the JSON recieved is valid. This result indicates that the simulation is placed in the process queue and as soon as a worker is available the calculation starts. For retrieving the results of the simulation or the current state of the job use 'hedo_simulations_read' with the jobID as path parameter. In case the validation fails, a detailed description of the missing or out of range property is returned.
* simulations_read[GET]: `https://api.meta-sys.ch/v1/hedo/simulations/{id}`
Returns the simulation results for the requested 'jobID'. Use polling until 'data' is not an empty array or the simulation failed with HTTP error code 500. As guiding value it takes arround 10 seconds until the results are available.
### Valuation
It requires two steps to perform a valuation. First call the `simulation_create` method which will return a job ID if the dataset is valid. Then we explicitly throttle the script execution of around 25 seconds, time needed to SREI to compute the valuation result. Secondly, we call the `simulations_read` method which will finally fetch the result of the valuation.
### Postal Codes
In order to valuate a property, SREI requires a tuple of zip code and city on the `simulation_create` method. This mapping
is returned with the `https://api.meta-sys.ch/v1/hedo/postalcodes/`. For performance issues, the result of this mapping
is stored internally on the project on the `plugins/kiiz/estimator/classes/estimator/srei/postalcodes.json` file.

!> This mapping should be updated from time to time, specially if you have a problem with a valuation that doesen't
recognize the postal codes.

## Common errors
From time to time, you'll receive error notifications from SREI that maybe a false positive.

SREI: Postal code combination ', 8090' not found. Maybe update the postal code definition?y

?> This meanse that the postal code 8090 was not found in the `postalcodes.json` definition because the user entered wrong
or falsy data. In most cases is just a false error and you can just ignore.
