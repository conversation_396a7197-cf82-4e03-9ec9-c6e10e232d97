# warning  20.01.24 not used in V3

Introduction
============
CIFI is a real estate estimator which provides a REST API to gather information from appartments and houses.

## API
### Endpoints
* test: <https://testservices.iazi.ch/api/models/swagger>
* prod: <https://services.iazi.ch/api/models/swagger>

A Swagger UI is available to perform API operations:
* test: <https://testservices.iazi.ch/api/models/swagger>
* prod: <https://services.iazi.ch/api/models/swagger>

### Query parameters
*modelDate*: Pour votre licence, laisser toujours vide
*predictMode*: Pour votre licence, fixe = 1
*deliveryTypeCode*: Pour votre licence, fixe = 2
*cultureInput*: RFC Code (fr-CH, de-CH, it-CH, en-US)
*cultureOutput*: RFC Code (fr-CH, de-CH, it-CH, en-US)


The API documentation is located [here](/API/CIFI/Service.Models-R_v1.4.pdf ':ignore').

### Particularities
 - The energetic norm doesen't change the valuation's price, it's just used by CIFI to gather user data.
