Introduction
============
Fahrlander is a real estate estimator which provides a REST API to gather information from appartments and houses.
In order establish a connection with their API, the application must provide a Token to ensure the security and
authenticity of the connection.

## API
You can find the REST API definition [here](https://imbas.fpre.ch/apidocs).
The enabled modules are
* Geocode
* EFH (Evaluation Model Single Family): Method for determining the market value of single-family homes.
* EWG

## Generate a Token

These elements are needed to call the new token:
* URL => ```https://login.microsoftonline.com/tools.fpre.ch/oauth2/v2.0/token```
* headers
  * Content-Type => ```application/x-www-form-urlencoded```
* form_params
  * grant_type => ```client_credentials```
  * client_id => ```<CLIENT_ID>```
  * client_secret => ```<CLIENT_SECRET>```
  * scope => ```https://tools.fpre.ch/<ID>/.default```
],
