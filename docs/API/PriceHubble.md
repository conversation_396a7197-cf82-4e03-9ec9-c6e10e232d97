Introduction
============
Price Hubble is a real estate estimator which provides a REST API to gather information from appartments and houses.

## API Doumentation
Make sure you read all the documentation of Price Hubble.
<https://docs.pricehubble.com/>
### Endpoints
* test: <https://api.pricehubble.com/api/v1> (see preprod authentification token in the config)
* prod: <https://api.pricehubble.com/api/v1/>

### Ressources
When performing a valuation the below REST resources are requested:
1. /location/info: Fetch microlocation grade  
1. /valuation/property_value: Fetch the property valuation
1. /dossiers: creates a property "Dossier" which gives access to an embedded Dashboard for the buyers.
1. /dossiers/links: Shares the previously created dossier

### Property Dossier
When a property is published, we request `/dossiers` with the property's data followed by `/dossiers/links`
to create a shareable link, it returns an URL to an embedable iframe that's used to show valuable microlocation
data to the buyers.
