Valuation Engine
================
The valuation engine is a set of modular classes and configurations. Each module is responsible for delivering
the valuation result with total abstraction of the other valuation institutes. This allows flexibility and maintainability.

## Architecture
The valuation engine is located under `plugins/kiiz/estimator/classes/estimator`.
- `\Kiiz\Estimator\Classes\Estimator\Plugin`: This is the Plugin abstract definition that orchestrates the configuration,
 the data transformation, the API, and the response Parser of each plugin. It’s most relevants methods are
  - `estimate()`: Uses the plugin’s API to get the valuation result.
  - `microlocation()`: Uses the plugin’s API to get the microlocation grade
- `\Kiiz\Estimator\Classes\Estimator\Transformer`: Pre transforms the user data into a format that is compliant with the
 valuation API specification.
- `\Kiiz\Estimator\Classes\Estimator\KiizPrice`: Calculates the kiiz prices given the result of the 5 valuation institutes.
- `\Kiiz\Estimator\Classes\Estimator\Form`: Computes the form fields to show to the end user. It takes as parameters the
 5 institutes `yaml` configuration. If one of the valuation plugin is disabled, the `Form` class automatically removes the unnecessary fields.

## Valuation Plugin
The valuation plugin is an extension of the `Kiiz\Estimator\Classes\Estimator\Plugin` class. It’s main purpose is to
customize if needed the behaviour of the base plugin. You can extend the `rules()` method for specific cases.
The `Api` class is responsible for all the REST/Soap calls to gather the valuations result.
The `config.yaml` is the mapping of the kiiz form and the Api destination fields. Look at the below example of field 
configuration: 

```
floor_nb:
        to: 'floorNb'
        rules: 'integer|min:-1|max:40'
        ppe:
            required: true

```

## YAML configuration
Below the complete description of the configuration of each field:
- `to`: is the name of the kiiz form field. The `to`property specifies the API destination field to be mapped.
- `rules`: contains the valudation rules (see Laravel validation) of the field.
- `ppe` specifies if the field is diplayed on the appartment’s form. If left empty, the field is optional, if you need to set it as mandatory, adde the `required: true` definition.
- `villa` specifies if the field is diplayed on the house’s form. If left empty, the field is optional, if you need to set it as mandatory, adde the `required: true` definition.
- `transformFrom`: If this field is defnied, the engine will automatically map this field’s name to a method defined in the `\Kiiz\Estimator\Classes\Estimator\{Institute}\Transformer` class.
- `mapping`: This is used only for the dropdown menu. It maps the forms user input against the API requirement.
- `scale` this field will map a method of the `\Kiiz\Estimator\Classes\Estimator\Scaler` class.

## YAML configuration generator
The each YAML configuration should be generated with the [API Field Mapping](https://docs.google.com/spreadsheets/d/1wzqOy_EAghfHrBZLAqOzhcQL6hK7YK226StMiKRDvDk/edit?usp=sharing) Google Spread Sheet.
For the sake of maintainability.
> Ask the owner of this file (<EMAIL>) account to grant you the permissions.

### Tab description
Below we describe the most relevant tabs for the valuation engine.
- `Fields`: Lists the complete fieldset of the valuation form. The left freeze columns are the actual kiiz fields 
that are stored in `plugins/kiiz/estimator/models/estimate/fields.yaml`. On the right unfreeze columns there's the mapping
of the 5 valuation institutes along with their configuration. This tab is computed by a Google Script that parses the Spreadsheet
and generates the YAML configuration that is stored in the `Estimation Fields Yaml` tab.
- `Estimation Fields Yaml`: Generates the configuration YAML for each institute. It calls the Google Custom Function `API_YAML`
that is defined in the script section of the spreadsheet (Tools > Script Editor > API YAML function). 
- `Enumerations`: Contains the mapping of the kiiz form along with the 5 institutes options.

