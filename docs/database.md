Database
========

Logging queries
---------------
To enable the query logging in your dev environment simply add the `config/dev/database.php` with the following content:
```php
<?php
return [
    /*
    |--------------------------------------------------------------------------
    | Log queries
    |--------------------------------------------------------------------------
    |
    | Enable this to log all the executed queries. This should be enabled only in dev environment
    |
    */
    'log' => true
];
```
This will log all the queries into the `storage/logs/sql-{date}.log` file.

Production backups
---------------
Infomaniak performs one backup each day at 04:00.
On top of that, we have our own backup system `bin/mysql-backup.sh` that is executed each hour.
Be sure that it is added in the crontab of the production server!  

Mayday procedure (database crash)
----------------
If you are confronted to a database crash, a wrong SQL query run by the junior dev or any kind of database problem, and 
you are in the urgency of restoring the database to a previous state, follow the below steps:
1. Set the website in maintenance mode, this will prevent the visitors/backend users from accessing the website. It
will also stop the `queue worker` from executing future tasks. 
    ```
    php artisan down
    ```
1. Stop the cron execution in the production server in order to avoid the communication to third party services with wrong
data.
   ```
   crontab -e
   ```
   Then comment all the lines and save the crontab.
1. Backup the actual database, ingore the `system_event_logs` table.
```

```
1. Warn the kiiz team (<EMAIL>) about the problem and tell them to avoid using the website.
1. Restore the database to the latest saving. All backups are stored in the production server `~/mysql-backups`.
```
cd ~/mysql-backups
gunzip me5du_prod_{date}_{time}.sql.gz
mysql -u me5du_prod -p -h me5du.myd.infomaniak.com me5du_prod < me5du_prod_{date}_{time}.sql
```
1. Disable the maintenance mode
```
php artisan up
```
1. Enable again the crontabs, uncomment all the lines in the cron tab
```
crontab -e
```
1. Try to manually restore the following tables, if needed
    - users
    - kiiz_estimator_estimates
    - kiiz_calendar_events
    - kiiz_calendar_event_booking
    - kiiz_calendar_event_booking_vists
