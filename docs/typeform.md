TypeForm
========================
[TypeForm](https://www.typeform.com/) is a cloud service that enhances the UX of form and helps to attract more
responses and a higher response rate. 

## Configuration
The form configuration is made online once you're logged in. You can have an overview of the main valuation forms at 
the below URLs:
- **Quick Valuation Form - PROD**: https://www.kiiz.ch/fr/quick-valuation?street=Avenue+de+Montchoisi+21&street_number=21&zip=1006 
- **Quick Valuation Form - PREPROD** https://preprod.kiiz.ch/fr/quick-valuation?street=Avenue+de+Montchoisi+21&street_number=21&zip=1006
- **Extended Valuation Form - PROD** https://www.kiiz.ch/fr/extended-valuation?unique_id=XXX&type=ppe
- **Extended Valuation Form - PREPROD** https://www.kiiz.ch/fr/extended-valuation?unique_id=XXX&type=ppe

The GET parameters of the urls are forwarded to the TypeForm embedded iframe as pre populated [hidden fields](https://help.typeform.com/hc/en-us/articles/360029114712-How-to-use-Hidden-Fields).
At the current time, only the FR forms are configured. If you wish to translate the form to another language, the form
must be duplicated and translated.

### Hidden fields
The hidden fields are used to populate the forms data. Their value will be sent to the Webhook (section below) as is, untouched.
This is usefull when you want to pass the address or tracking information.
#### Quick Valuation Form
- unique_id: Generated unique ID. It's used to match the quick valuation with the extended valuation
- street: Name of the street
- street_number: Number of the street
- zip: Zip code
- gclid: Google unique click identifier. This comes from Google Ads.
- campaignid: Google Ads campaign ID, present for Display campaigns
- adgroupid: Google Ads Group ID, present for Display campaigns
- adid: Google Ads Ad ID, present for Display campaigns
- fbclid: Facebook unique click identifier. This comes from Facebook.
- ip_id: Instapage page ID. This is populated when the user comes from an Instapage ID.
- utm_source: UTM campaign source, should always be present when creating an Ad Campaign.
- utm_medium: UTM campaign medium, should always be present when creating an Ad Campaign.
- utm_campaign: UTM campaign name, should always be present when creating an Ad Campaign.
- locale: Current language of the website (fr, de, it)
#### Extended Valuation Form
- type: Type of the property, `ppe` (appartment) or `villa` (house)
- unique_id: Previously generated unique_id (see Quick valuation form hidden fields). This will match both forms when the
form is submitted.
- locale: Current language of the website (fr, de, it)

### Form Mapping
Regular form fields like text or numbers are pretty easy to configure. The only thing the needs your attention is the
`Question reference` that is actually mapped with the Valuation database fields.
> Always set the question reference, and if possible, make it correspond with the `\Kiiz\Estimator\Models\Estimate.request_data`
property.

The mapping between the `Question reference` and the valuation model happens in the `\Kiiz\Estimator\Classes\TypeFormParser`
class.

#### Dropdown mapping
When configuring dropdowns, the choices must correspond exactly with ones defined in the `kiiz_backend_enum_items`. 
Unfortunately at the present time, there's no possibility to set a named code for each choice. You can see this behaviour
in action in the in the `\Kiiz\Estimator\Classes\TypeFormParser` class.

## Use case
When a customer enters the valuation process, he's actually using TypeForm to input all his data. The TypeForm is 
embedded with an iframe given a form ID (see `/config/typeform.php` ). We pass the address parameters in the URL to
initialize the form and the TypeForm handles the rest. Once the form is submitted, TypeForm sends the data via a 
Webhook. The result is handled by the `\Kiiz\Estimator\Controllers\TypeFormWebhook::store()`
method.

## Webhooks
Please take a look at the [Webhook Documentation](https://developer.typeform.com/webhooks/) to understand how they work
and how to troubleshoot them.
