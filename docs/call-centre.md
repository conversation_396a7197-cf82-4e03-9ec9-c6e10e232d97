CALL CENTRE
==========
## Import customers phone numbers
Here is the procedure to import latest Kiiz customers phone numbers in our Mitel call centre.
It is possible to create a maximum of 1000 contacts.
 
- Launch a browser and go to https://192.168.1.13 (login access in Keepass) to access the call centre admin.
- Click on "Annuaire personnel / Phone book" and export all contacts by clicking on "Exportation / Export".
- Open the downloaded file (ExportSheetAbbrDialList.xlsm) and click on the "Abbreviated dialing list" tab. 
You should see the same contacts that are listed on the "Annuaire personnel" page.
- Delete the cells values (Keep the first and last column) for the "Abbreviated dialing number" above 3000. 
The numbers under 3000 are not customers from our database and were created manually.
- Open your MySQL client and execute the following request:
```
SELECT 
	@rownum := @rownum + 1 AS row_number,
	REPLACE(REPLACE(phone_number, '+', '000'), ' ', '') AS user_phone_number, 
	TRIM(CONCAT(IFNULL(surname,''), ' ', IFNULL(`name`, ''))) AS user_name
FROM `users` u
	LEFT JOIN `kiiz_property_property` p ON u.id = p.client_id
	LEFT JOIN `kiiz_estimator_estimates` e ON u.id = e.user_id
	LEFT JOIN `kiiz_calendar_event_booking_visits` v ON u.id = v.client_id
	CROSS JOIN (SELECT @rownum := 3000) r /* init variable */
WHERE 
	phone_number IS NOT NULL AND phone_number != '' AND
	(p.id IS NOT NULL OR e.id IS NOT NULL OR v.id IS NOT NULL)
GROUP BY user_name
HAVING user_name != ''
ORDER BY row_number;
```
- Copy all rows, paste it after the last "Abbreviated dialing number" and save the file.
- In the call centre admin, click on "Import", check the option "Remplace la configuration existante   
 / Replace existing configuration" and select your Excel file.

