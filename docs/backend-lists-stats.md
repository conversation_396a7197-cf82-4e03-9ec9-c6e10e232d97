Stats lists
=====
The controller behavior `Kiiz\Backend\Behaviors\ListStatsController` can be used to display a simple table of statistics.
It is based on `Backend\Behaviors\ListController` and removes all the unecessary options (pagination, column sorting, etc...).

A new option `showTotal` is provided by this controller to add a row at the bottom of the list to display the total of each column.
It is enabled by default but it can be disabled in the config of the list `config_list.yaml` if needed.
In `columns.yaml` you must add `showTotal: true` for each column you want to see the total.
