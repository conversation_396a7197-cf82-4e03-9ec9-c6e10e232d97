Development Cycle
=================
We use `git flow` to work in an active and reliable iteration system.

The following guidelines must be followed by all the team members in order to ensure reliability and fluidity of the
current and future developments
## Working on a new issue
Each time that <PERSON> asks for a new feature, it should be added as a new issue on JIRA's back log. When you start
working on an issue, you must create a new feature branch using `git flow`. The name of the branch should be `feature/{issue_number}`.
The branch should always be pushed to `origin` so the other devs can also work/review the branch. When you are ready to
test on preproduction, you should merge the feature branch in the `preprod` branch, push your changes and deploy.

Once the feature has been validated on preproduction, you should `finish` the feature using `git flow`. The branch will be
merged into `develop` and will be available on the next release.

<p style="color:red">Never merge the preprod branch into develop or any other feature!! It could result in puting all the
current development into production!!</p>

## Deploying a release
#### Creating a release
When you are ready to deploy a new release, check the current last tag of the project with `git-last-tag` and depending
on the kind of release (minor, major, hotfix) increment the version. Then create a new release with `git flow` and finish it.
This will create a new tag and will merge the release branch into `master` and `develop`.

In order to push the new tag and both `develop` and `master` branches into `origin`, it's highly recommendated to use the
commande `git-push` that will push both tags and branches.

#### Updating the preproduction environment
Login with `ssh` into the kiiz server and run the following command lines:
```bash
cd preprod
bin/deploy.sh
```
Done!

#### Updating the production environment
Login with `ssh` into the kiiz server and run the following command lines:
```bash
git pull
git checkout {new_tag}
bin/deploy.sh
```
Done!
