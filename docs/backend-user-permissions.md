# User's roles and permissions

## Create a role
To create a role you need to create a migration in `plugins/kiiz/backend/updates`.<br/> 
Here is an example of a migration class:
```
class SeedUserGroups2Table extends Migration
{
    public function up()
    {
        UserRole::create([
            'name' => 'Account',
            'code' => 'account',
            'description' => 'Gestion de comptes clients',
            'is_system' => true
        ]);
    }

    public function down()
    {
        \Db::table('backend_user_roles')->whereIn('code', ['account'])->delete();
    }
}
```
