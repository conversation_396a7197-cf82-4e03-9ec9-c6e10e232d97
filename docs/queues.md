Queues
======
In the kiiz platform, all the transactional emails should be sent with the `Mail::queue` method to ensure the
delivery if the mail server is down. Please read [the mail queue documentation](https://octobercms.com/docs/services/mail#queueing-mail).

October CSM also provides other ways of handling parallel processes with the [Queue system](https://octobercms.com/docs/services/queues).
In our platform (preprod & prod), the queues are launched with `supervisor` to ensure that the process is always running.

Prod & preprod
--------------

```bash
/opt/php7.1/bin/php /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/prod/artisan queue:work --tries=10
/opt/php7.1/bin/php /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/preprod/artisan queue:work --tries=10
```

If you need to reboot a queue worker for any reason (high memory usage for example), you may just kill the process and it
will automatically relaunched with `supervisor`.
The below command line kills both preprod and prod processes.

```bash
pkill -f "artisan queue:work"
```

Development
-----------
Login in your docker container and run the below command:
```bash
/usr/local/bin/php /var/www/html/artisan queue:work --tries=10
```

### october cms V3 / new server kiiz-2 (14-01-2024) ###

preprod
--------------

```bash
/opt/php8.1/bin/php /home/<USER>/7b6085b5eaf61bba7c34d93d5b7937cd/sites/dev.kiiz.ch/artisan queue:work --tries=10
```

If you need to reboot a queue worker for any reason (high memory usage for example), you may just kill the process and it
will automatically relaunched with `systemctl --user restart queue.service`.
The below command line kills both preprod and prod processes.

```bash
pkill -f "artisan queue:work" or systemctl --user stop queue.service
```
