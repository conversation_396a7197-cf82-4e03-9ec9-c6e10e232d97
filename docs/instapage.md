Instapage
=========
Build personalized, mobile-responsive, and conversion-optimized post-click landing pages in minutes—without a developer.  
We use Instapage to experiment different designs over a Google Ad or Facebook Ad campaign. Please make sure you read
all their [documentation](https://instapage.com/resources).

## Implementation
We use Instapage as landing pages for our ads (Facebook, or Goolge). A usual scenario is when a customer searchs for a free
valuation, then he clicks on an ad on Google, and finally redirected to an Instapage landing page.  
Once the form is submitted, the user is redirected to a page that embeds a [TypeForm](typeform.md) that will collect the 
user's property data. 

## Tracking
The most important thing to keep in mind while building a landing page, is the conversion tracking configuration. Make
sure that the below parameters are setup correctly:
- **UTM**: The UTM parameters should **always** be present for the Ads campaigns in order to have a proper tracking.
- **Conversion Goals**: This must be set to **external**. It will show you a script to implement on the kiiz website. This step
is already handled in the partial view `themes/kiiz/pages/estimator/quick-valuation-confirm.htm`. 
- **Analytics**: Make sure Google Ads is setup, Google Ads or Facebook Ads, depending on the source of the customers.
- **Facebook Pixel**: Make sure is set up
- **Javascrip**: For address form submission landing pages, add the below script:
```
window.instapageFormSubmitSuccess = function (form) {
    var instaPageId = {{instapageId}}; //hardcode your instapage ID here, get it from the url
    // google click ID
    var gclid = new URL(window.location.href).searchParams.get("gclid");
    var campaignid = new URL(window.location.href).searchParams.get("campaignid");
    // utm_source
    var utm_source = new URL(window.location.href).searchParams.get("utm_source");
    // utm_medium
    var utm_medium = new URL(window.location.href).searchParams.get("utm_medium");
    // utm_campaign
    var utm_campaign = new URL(window.location.href).searchParams.get("utm_campaign");
    // get values from form fields
    var street = window.__page_generator ? $(form).find('input[name="' + 'Rue' + '"]').val() : $(form).find('input[name="' + base64_encode('Rue') + '"]').val();
    var streetNumber = window.__page_generator ? $(form).find('input[name="' + 'Numéro' + '"]').val() : $(form).find('input[name="' + base64_encode('Numéro') + '"]').val();
    var zip = window.__page_generator ? $(form).find('input[name="' + 'Code postal' + '"]').val() : $(form).find('input[name="' + base64_encode('Code postal') + '"]').val();

    // Get the actual redirect url
    var redirect_url = ijQuery(form).find('input[name="redirect"]').val();

    // Amend redirect url in form by adding fields from form
    redirect_url = redirect_url + '?street=' + street + '&street_number=' + streetNumber + '&zip=' + zip + '&ip_id=' + instaPageId;
    // appending Gclid
    if (gclid) {
        redirect_url += '&gclid=' + gclid;
    }
    if (campaignid) {
        redirect_url += '&campaignid=' + campaignid;
    }
    if (utm_source) {
        redirect_url += '&utm_source=' + utm_source;
    }
    if (utm_medium) {
        redirect_url += '&utm_medium=' + utm_medium;
    }
    if (utm_campaign) {
        redirect_url += '&utm_campaign=' + utm_campaign;
    }
    $(form).find('input[name="redirect"]').val(redirect_url);
}
// form validation
window.onload = function() {
    var zip = $('input[name="Code postal"]');
    zip.attr('type',  'number');
    zip.attr('min', 1000);
    zip.attr('max', 9999);
}
```
- **Test**: Once the page is setup, you can publish it and test it before routing the Ads towards this page.
