Lists
=====
We added customized columns types.
## Column types
Before adding a new custom type make sure that it's not provided by October CMS 
[here](<https://octobercms.com/docs/backend/lists#column-types>).
If you need to add a custom column format you have to follow these steps
1. Declare the formater in `\Kiiz\Backend\Helpers\View`.
1. Register the new column format in the `\Kiiz\Backend\Plugin::registerListColumnTypes()` method.
    ```php
    return [
        'm2' => [$viewHelper, 'm2'],
        'nbrooms' => [$viewHelper, 'nbRooms'],
        // add here new great formats
    ];
    ```
1. Then use them in the `columns.yaml` of your list
    ```yaml
    nb_rooms:
        label: Nb rooms
        type: nbrooms
    ```
## Available column types
### actions
Adds a dropdown button with custom actions contextual to the record. Below an example:
```yaml
actions:
    label: ''
    type: actions
    sortable: false # don't remove this
    nolink: true # don't remove this
    clickable: false # don't remove this
    actions:
        #custom action that sends an email
        send-mail:
            url: kiiz/mailer/mails/create
            label: 'kiiz.property::lang.action.send_email'
            params:
                client: $.client_id #when prefixed with $, it will take the value of the current model
            # all other options will be injected as attributes in the A HTML tag
            target: _blank
```
### m2
Suffixes the text with ².
### nbrooms
Adds a decimal zero to a number.
### html
Allows a column to be displayed as HTML with some basic tags.
### translation
Seeks in a translation column for a model that implements the `RainLab.Translate.Behaviors.TranslatableModel`.
```yaml
label_de:
    label: 'kiiz.backend::lang.label_de'
    type: translation
    lang: de
    valueFrom: label
    sortable: false
``` 
### currency
Suffixes a column with CHF
### status
Misc status colors and a link.
```yaml
status:
    label: kiiz.user::lang.followup.estimate_status
    type: status
    plugin: estimator
    link: kiiz/estimator/estimates/preview/:id
    linkLabel: Voir
    idField: estimate_id
    icon: 'oc-icon-circle' # optional, default oc-icon-circle
    extraClass 'my-extra-class' # optional
``` 
### address
Formats an entity as an address
### image
Displays an image
### enum
Seeks an enum on the list
### daysago
Shows the number of elapsed days. Works only on date fields
### presenter
Uses the presenter method of a model:
```yaml
type: presenter
method: someMethod
```
