Front End Components
====================

## Auto Login
If you wish to create a page wich handles a user's autologin given an url, you must include in your page the `autologin`
component prior to all other components. The url must contain two `GET` parameters :
 - **token**: Generated token for a user
 - **u**: ID of the user
The final url may look like `http://localhost/fr/calendar-photos?token=5VOdMwumVQTJCVssZELNE36Wh8ZvSeC956C2UhwoqF9VR9e2&u=56`

Then in your page you must include the `autologin` component like this:
```
title = "Wizard Calendar Pictures"
url = "/calendar-photos"
layout = "layout"
is_hidden = 0

[autologin]
error_page = "page-to-forbidden-page  // optional"
...
==
```
The component allows an optional property `error_page` that specifies a custom page to render in the case that the token
is invalid. It it's not specified, it will just render the 403 page.
### Token generation
You can generate the auto login token of a user like this:
```php
<?
$token = $user->createToken(
    $user::TOKEN_AUTO_LOGIN,
    $user::TOKEN_AUTO_LOGIN_EXPIRATION
);
```
For further information on token generation have a look on [token generation](backend-helpers.md#generating-token)
