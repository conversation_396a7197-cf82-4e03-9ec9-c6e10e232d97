# Optimizing videos
Each video must be optimized and converted to `webm` and the `mp4` should be compressed

## Command line tool FFMPEG
First install `fmpeg`
```
sudo apt-get install ffmpeg
```
### Convert from mp4 to webm
```
ffmpeg -i source.mp4 -c:v libvpx-vp9 -crf 30 -b:v 2000k destination.webm
```
### Optimize mp4 for streaming
```
ffmpeg -y -i source.mp4 -c:v libx264 -b:v 2600k -pass 1 -c:a aac -b:a 128k -f mp4 /dev/null && \
ffmpeg -i source.mp4 -movflags faststart -c:v libx264 -b:v 2600k -pass 2 -c:a aac -b:a 128k output.mp4
```
