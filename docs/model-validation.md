Introduction
============
The Basic Rainlab Model class can implement a trait that enables automatic validation if a set of rules is defined in 
the `public $rules` property. This is good but not very developer friendly as the rules have to be handled in a different
file that the YAML field definition `mymodel/fields.yaml`.

We have created a trait `\Kiiz\Backend\Traits\YamlValidable` that allows the model to get a ruleset from the `fields.yaml`
backend definition. The fields of the YAML file that have a `rules` property will be validated when the model is saved. 
You can use the [Laravel's validation rules](https://laravel.com/docs/5.6/validation#available-validation-rules).
An example of an YAML definition :
```yaml
fields:
    zip:
        label: 'Zip code'
        type: number
        span: auto
        required: true
        # these rules will be handled while the model is validated
        rules: required|digits:4
```
In your model you have to implement the `\Kiiz\Backend\Traits\YamlValidable` trait with the path of the YAML file definition.
```php
class Object extends Model
{
    use \October\Rain\Database\Traits\Validation;
    // initializes the model's rules from the fields.yaml definition instead of the model himself
    use \Kiiz\Backend\Traits\YamlValidable;
    // path to fields definition
    protected static $yamlConfig = 'kiiz/property/models/object/fields.yaml';
}
```
