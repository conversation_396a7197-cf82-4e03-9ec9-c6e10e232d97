FrontApp Integration
========================
With Front, customers are happier, employees feel more effective, and internal onboarding is easier because everything 
is in one product.

We use Front for email and SMS communication with the customers. Every single conversation can be accessed by the team
thus improving and optimizing the collaboration of the team. 

## Contact e-mail addresses
The inbound emails should be sent to the below emails to be synchronized with the Backend:
 - Prod: <EMAIL>
 - Preprod: <EMAIL>

## Conversation synchronisation
Every single conversation with the client is automatically synchronized with the Backend via Webhooks.
Webhooks happen when for outgoing or inbound conversations, for both email and SMS.
> Make sure to read the [Webhook documentation](https://dev.frontapp.com/reference/webhooks)

The Webhooks are configured in the Front's settings > (Company) > Rules > "Injects Message in October CMS".

The webhooks are handled in `\Kiiz\Frontapp\Controllers\Webhooks::store()` and are stored in the `kiiz_user_contact` table.
You can plug custom event listeners on `kiiz.frontapp.conversation`.

## API
We use the Front's API to send all communications made to the client. The emails/SMS are sent to the API and Front
handles the sending of the communication through the Infomaniak's SMPT server for e-mails, and [Twilio](twilio.md) for SMS.
> Make sure to read the [API Documentation](https://dev.frontapp.com/).

### Email
The e-mails are send transparently using the default October CMS [queue system](https://octobercms.com/docs/services/mail#queueing-mail).
They are then catched by the `\Kiiz\Frontapp\EventHandlers\SendMail` class that will check if the email has to be sent
via Front (`frontapp.apiViews` configuration) or the regular SMPT server. 

### SMS
The SMS are sent using the Front's API and the SMS channel (see `frontapp.smsChannelId` config). Behind the scenes,
the SMS is actually delivered with [Twilio](twilio.md#sms-delivery). If you have any issues with the SMS delivery,
you can check the Twilio Webhook configuration on the Front's settings (Settings > Inboxes > SMS > Overview > SMS). 
There you can see the Twilio's number that's used to deliver SMS.

![SMS Settings](assets/frontapp-sms-settings.png)  

The actual SMS delivery flow:
> Backend > Front API > Twilio API > SMS Delivery 

But you don't really have to worry about what happens behind the scenes. To deliver an SMS with is as easy as the code
below: 
```
$frontappMessenger = \App::make(\Kiiz\Frontapp\Classes\Messenger::class);
$frontappMessenger->sendSms('+41795845249', 'Hello world!', \BackendAuth::getUser());
```

## Contact synchronisation
When a new customer registers in the system, his information (name, email, phone) is automatically synchronized with Front via the
`\Kiiz\Frontapp\Jobs\SynchronizeContact::fire` job. This job is fired whenever there's a change on one of those fields.

## Kiiz Plugin
We've developed so far a plugin that summarizes the customer's backend data into a small mini app made with VueJS.  
![Plugin](assets/frontapp-plugin.png)  
This plugin summarizes contextual information about the contact, which is a powerful tool to optimize the backend user's
performance.

### Development
The web application hosted in the kiiz server is accessible trough `https://{{host}}/plugins/kiiz/frontapp/assets/dist/index.html?v=1.55.2`
but you won't be able to access it like that because of security.

> Make sure you read the [plugin's documentation](https://dev.frontapp.com/plugin.html)

In order to visualize the plugin in your `dev` environment, you'll need to create an account and install [ngrok](https://ngrok.com) to create
a public secure tunnel to your localhost machine.  
Then run `ngrok http 80` to expose your http server to the world. The command will prompt some temporary server to 
work on.

```
Session Status                online                                                                                                                                                                                                   
Account                       David Galvis (Plan: Free)                                                                                                                                                                                
Version                       2.3.35                                                                                                                                                                                                   
Region                        United States (us)                                                                                                                                                                                       
Web Interface                 http://127.0.0.1:4040                                                                                                                                                                                    
Forwarding                    http://51b4e934.ngrok.io -> http://localhost:80                                                                                                                                                          
Forwarding                    https://51b4e934.ngrok.io -> http://localhost:80    
```

Then you'll need to copy the secure server (https://51b4e934.ngrok.io in this example) in the Front plugin configurations.
Make sure to always use the `preproduction` account to test, NEVER ever the production environment.  
In Front, go to Settings > Plugins & API > Kiiz
Then replace the endpoint to 
```
https://51b4e934.ngrok.io/plugins/kiiz/frontapp/assets/dist/index.html
```

Then you're all set! When you start navigating through the conversations, it will automatically load the plugin in your
dev environment so you can start working on cool stuff.
