Introduction
============
Follow this guide in order to quickly install your dev environment using Linux (Debian) and Docker.

Requirements
------------
1. Install dependencies if it's not done yet. This will allow you to run php commands from your Terminal
```
sudo apt-get install git git-flow php php-mysql php-sqlite3 php-curl php-json php-mbstring zlib1g-dev php-zip php-xml php-xdebug build-essential php-zip php-soap graphviz
```
1. Install Composer 
```
curl -sS https://getcomposer.org/installer | sudo php -- --install-dir=/usr/local/bin --filename=composer
```
1. Install Docker CE <https://docs.docker.com/engine/installation/linux/docker-ce/debian/> and follow the procedure.
1. Install Docker compose <https://docs.docker.com/compose/install/>
1. Post Docker installation <https://docs.docker.com/install/linux/linux-postinstall/>
1. Install NodeJS and NPM <https://linuxconfig.org/how-to-install-nodejs-on-debian-9-stretch-linux>
1. Install gulp 
```
npm install -g gulp
```
Installation
------------
1. Create a directory where the project will be stored
1. Get source code gitlab 
    ```
    <NAME_EMAIL>:kiizch/web.git .
    ```
1. Copy `.htaccess.dev` to `.htaccess`
   ```
   cp .htaccess.dev .htaccess
   ```
1. Copy `.env.dev` to `.env`
   ```
   cp .env.dev .env
   ```
1. Run Docker containers 
    ```
    docker-compose up -d
    ```
1. Install composer dependencies 
    ```
    composer install
    ```
1. Run the Laravel autocomple helpers
    ```
    php artisan ide-helper:generate
    php artisan ide-helper:meta
    ```    
1. Ask the admin developer for a data base, install it and run 
    ```
    php artisan db:wipe-data
    ```
   This will wipe the original client's data (email and phone numbers). Verify that their information was correctly wiped
   in the `users` table.
1. Set directory rights 
	```bash
	chmod +x bin/set-permissions.sh 
	bin/set-permissions.sh dev
	```
1. Run docker containers with Docker Compose
    ```bash
    docker-compose up -d   
    ```
1. Create testing database for unit tests
    ```sql
    CREATE SCHEMA `kiiz_testing` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ;
    ```
1. Grant the user db user `kiiz` the MySQL rights to access the database `kiiz_testing`
1. Go to <http://localhost/backend> and login with admin/admin to check if everything is working fine
1. Copy the production assets in your local environment, where `kiiz` is the remote host ssh alias.
```bash
rsync -rvth kiiz:prod/storage/app/uploads/ storage/app/uploads/
```
You may run this command again to synchronize the file difference between your local environment and the production server.

Useful bash aliases
-------------------
Add the following commands into your `~/.bash_aliases` file
```bash
# debugs php in CLI mode
alias phpx='php -dxdebug.remote_enable=1 -dxdebug.remote_autostart=On -dxdebug.idekey=netbeans-xdebug -dxdebug.remote_host=localhost'
# push all branches and tags into origin. Do this after every release
alias git-push='git push --tags && git push --all'
# fetchs the last tag
alias git-last-tag='git describe --tags `git rev-list --tags --max-count=1`'
# execute gulp for the current project
alias gulp-local='node_modules/gulp/bin/gulp.js'
# gulp build backend
alias gulp-backend='cd {project_root}/plugins/kiiz/backend/assets && gulp build && gulp'
# gulp build frontend
alias gulp-frontend='cd {project_root}/projects/kiiz/themes/kiiz && gulp'

``` 

Code quality
------------
The following tools are highly recommended to improve the code quality
1. Install PHPUnit <https://phpunit.de/manual/current/en/installation.html>
1. Configure MessDoctor <https://confluence.jetbrains.com/display/PhpStorm/PHP+Mess+Detector+in+PhpStorm>
1. Configure Code Sniffer <https://confluence.jetbrains.com/display/PhpStorm/PHP+Code+Sniffer+in+PhpStorm>
