Presenters
==========
The presenters are a nice and elegant way to display a model's data in the UI. This helps to separate UI concerns and 
model's concerns. For example, you may wish to display the full user name with 

So you have those scenarios where a bit of logic needs to be performed before some data (likely from your entity) is displayed from the view.

- Should that logic be hard-coded into the view? **No**.
- Should we instead store the logic in the model? **No again!**

Instead, leverage view presenters. That's what they're for! This package provides one such implementation.

## Usage

The first step is to store your presenters somewhere - anywhere. These will be simple objects that do nothing more than format data, as required.

Here's an example of a presenter.

```php
use \Kiiz\Backend\Classes\Presenter\Presenter;

class UserPresenter extends Presenter 
{
    public function fullName()
    {
        return $this->first . ' ' . $this->last;
    }

    public function accountAge()
    {
        return $this->created_at->diffForHumans();
    }

}
```

Next, on your entity, pull in the `\Kiiz\Backend\Classes\Presenter\PresentableTrait` trait, which will 
automatically instantiate your presenter class.

** The presenters should be stored on the `presenters` directory of your plugin (`kiiz/{plugin}/presenters`)

Here's an example - maybe a Laravel `User` model.

```php
<?php

class User extends \Eloquent {

    use \Kiiz\Backend\Classes\Presenter\PresentableTrait;

    protected $presenter = UserPresenter::class;

}
```

That's it! You're done. Now, within your view, you can do:

```php
    <h1>Hello, {{ $user->present()->fullName }}</h1>
```

Notice how the call to the `present()` method (which will return your new or cached presenter object) also provides the benefit of making it perfectly clear where you must go, should you need to modify how a full name is displayed on the page.

You may also want to use a PDF or API presenter for your model. This should be stored in different presenter class.

Then in your view you can access other presenters with the `present($presenterClassName)`.
```php
    <h1>Hello, {{ $user->present(UserPdf::class)->fullName }}</h1>
```
