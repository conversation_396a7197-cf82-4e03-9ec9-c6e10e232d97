Emails
======
## Creating/Updating a new email layout
The update/creation of an email layout is done with database migrations. This should be done in the `Kiiz.Backend` plugin.

First update/create the css and html files in the `kiiz/backend/updates/mail/` directory.
Then create a new migration file with the below content and replace the `$layoutCode`, `$cssPath` and `$layoutHtmlPath` if needed.
```php
<?php namespace Kiiz\Backend\Updates;

use Kiiz\Backend\Classes\Mail\MailLayoutSeeder;

class KiizLayoutUpdate extends MailLayoutSeeder
{
    /**
     * Layout code
     * @var
     */
    protected $layoutCode = 'kiiz';

    protected $cssPath = 'kiiz/backend/updates/mail/kiiz-layout.css';

    /**
     * Path to the html layout
     * @var
     */
    protected $layoutHtmlPath = 'kiiz/backend/updates/mail/kiiz-layout.htm';
}
```
