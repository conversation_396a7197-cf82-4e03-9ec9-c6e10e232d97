Unit Tests
==========
The critical parts of the Kiiz Plugins should be tested to enhance deployments and code quality.
Make sur you've read all the [October CMS documentation](https://octobercms.com/docs/help/unit-testing) before going forward. 

The unit tests are stored in the `plugins/Keey/<plugin>/tests` directory. Use the `plugins/Keey/<plugin>/tests/fixtures`
to store mock data for your tests.

All the Tests classes must extend the `Kiiz\Backend\Tests\Unit\Base` class to benefit to a MySQL database instead of 
SQLite.

The tests should follow the folder structure of the tested classes.

## Useful links
- Mocking / Test Doubles: https://phpunit.de/manual/5.7/en/test-doubles.html
