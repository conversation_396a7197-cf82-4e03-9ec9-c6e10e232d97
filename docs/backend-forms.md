Backend Forms
=========

Fields ACL
---
In any advanced application there's a business need to hide/show form fields when a backed user has a specifi access or
not.
To achieve this, you can add a specific definition in the `fields.yaml` model fields definition.

Example:
```yaml
street:
    label: 'kiiz.estimator::lang.label.street'
    type: text
    span: auto
    tab: 'kiiz.property::lang.tab.client_data'
    required: true
    rules: required
    permissions:
        update: kiiz.property.can_edit

propertyDocuments:
    span: left
    path: field_property_documents
    type: partial
    tab: 'kiiz.property::lang.propertydocuments.plural.label'
    permissions:
        update: kiiz.property.can_edit
        preview: kiiz.property.can_edit
```
You can set the option `permissions` with the form context (update, preview). Therefore in the `street` field of our
example, we show the field only to users who have the `kiiz.property.can_edit` permission. In our second example, we
show only the `propertyDocuments` in the preview and edition, to users who have the `kiiz.property.can_edit` permission.

The code who handles this behaviour is stored in the `\Kiiz\Backend\EventHandlers\FormFieldPermissions` class.

## Record Finder
The standalone October CMS [record finder](https://octobercms.com/docs/backend/forms#widget-recordfinder) is improved to allow
a record creathin within the widget. For this, you have to add the option `recordCreateUrl` which points to the controller
who handles the record creation. The controller has to implement the behaviour `Kiiz\Backend\Behaviors\AjaxFormController`
to allow CRUD operation within an ajax popup. Here's an example on how to achieve this:
```yaml
client:
    label: 'kiiz.calendar::lang.client'
    oc.commentPosition: ''
    nameFrom: name
    descriptionFrom: description
    list: $/kiiz/user/models/user/record_finder_columns.yaml
    span: full
    type: recordfinder
    baseUrl: /kiiz/calendar/events/calendar #base url where the record finder is located, this is important for cross-controller requests
    recordCreateUrl: /kiiz/user/users #(optional) url to controller
    recordCreateTitle: 'kiiz.calendar::lang.my_custom_title' #(optional) Title of the create form
    recordCreateData: # (optional): extra data to populate the creation form
        name: 'Juanito'
        last_name: 'Pepito'
```
### Options
 - **baseUrl**: base url where the record finder is located
 - **recordCreateUrl**: url to controller
 - **recordCreateTitle**: (optional) set a custom form title with a translation key
 - **recordCreateData**: (optional) extra data to populate the creation form

Here's a sample of the controller:
```php
<?
class Users extends Controller
{
    public $implement = [
        'Backend\Behaviors\ListController',
        'Kiiz\Backend\Behaviors\AjaxFormController',
    ];
}
```

## Presenter
This field type allows you to use the [presenters](presenters.md) as a form field. Use the `presenter` type on your field.
```
calendar_autolog_link:
    label: 'My presenter label'
    type: presenter
    method: calendarLink # presenter method
    relation: client #optional, to define a relation to present
    options:
        embedLink: false #optional, default false
        escapeHTML: true #optional, default true
    context: preview
    span: auto
```
### Options
- **method**: presenter's method
- **relation**: Optional, define the relation to present
- **options.embedLink**: Optional, embeds the result of the presenter with a link HTML tag. If true, the option
`escapeHTML` must be set to `false`
- **options.escapeHTML**: Optional, escapes or not the HTML result of the presenter

## Inline rich text editor
The rich inline text editor allows the end user to edit a field in a more confortable way.
```html
<!--Inline editor-->
<form-richeditor
    id="comments"
    name="comments"
    value="<?= e($formModel->comments) ?>"
    handler="/backend/api/property"
    model-id="<?= $formModel->id ?>"
></form-richeditor>
```
### Options
- **id**: HTML id of the element
- **name**: HTML name, this will be sent in the REST request as the key parameter
- **label**: label of the input
- **placeholder**: Element placeholder when empty
- **value**: default value
- **handler**: REST path to resource for AJAX calls
- **modelId**: model ID

## Multiselect
```html
<form-multiselect
        source-url="/backend/api/property?status=published"
        name="property_id"
        placeholder="Filtrer par client"
        selection="clients sélectionné(s)"
        :on-close="refresh"
        :option-formatter="propertyFormatter"
    ></form-multiselect>
```
### Options
- **name**: HTML name, this will be sent in the REST request as the key parameter
- **placeholder**: HTML placeholder of the select
- **selection**: Text that will be shown when there's itmes selected
- **on-close**: Javascript callback function that will be called when the element is closed
- **option-formatter**: Javascript callback function that will format the object into an id/label pairs: `{id: 5, label 'Jean-Patrick Marcel''}`

## Fileupload
- The property maxFilesize has been added to limit the size of images in the property gallery.
- If the page fails to load after uploading images due to @imagecreatefromjpeg failing when resizing large images, review the memory limit and upload_max_filesize.
