# Workflows
There are two main workflows defined for Kiiz.
## Valuation
![Valuation](workflow/estimator.png)
## Property
![Property](workflow/property.png)
## Property Offer
![Property](workflow/property_offer.png)
## Property Interested
![Property](workflow/property_interested.png)
## Configuration
Custom workflows can be defined in the `config/workflow.php` configuration file. For further information check the 
[Laravel Workflow](https://github.com/zerodahero/laravel-workflow) package. 
**Transition options**  
On each transition you can specify a custom options

| Option        | Description   |
| ------------- |:-------------:|
| formFields    | Path to .yaml file that contains the field specification to show on transition form |
| populate      | List of fields that populate the form with the model's value      |

fields that will show on the popup with the `formFields` property.  

When the workflows are updated/modified, please update the `docs/workflow/*.png` images by dumping the workflows using 
the below commands :
```bash
php artisan workflow:dump estimator --class="Kiiz\Estimator\Models\Estimate"
php artisan workflow:dump property --class="Kiiz\Property\Models\Property"
php artisan workflow:dump property_offer --class="Kiiz\Property\Models\PropertyOffer"
php artisan workflow:dump property_interested --class="Kiiz\Property\Models\PropertyInterested"
```

This will created the images at the root of the project. Then you must move the images to the `docs/workflow` folder.
## Events
The Worflow manager fires events when the transitions are performed. 
If you want to perform some action when the property is published, you can listen to the `workflow.property.transition.publish`
event of the Plugin.

Please refer to the `\ZeroDaHero\LaravelWorkflow\Events\WorkflowSubscriber` for the full list of events.
