Twilio integration
==================
Twilio is a cloud communications platform as a service (CPaaS) company based in San Francisco, California. 
Twilio allows software developers to programmatically make and receive phone calls, send and receive text messages, and perform other communication functions using its web service APIs.

In the context of kiiz, we use <PERSON><PERSON><PERSON> for SMS phone verification ([Authy](https://www.twilio.com/authy)), SMS delivery
and Whatsapp communication (not yet in production).

## Phone numbers
We've bought so far two phone numbers on Twilio to communicate with our customers:
- **SMS** +41 79 807 46 89
- **Whatsapp** +41 79 807 08 40 (not on production)

Unfortunately, on Front we can't set up the same phone number to deliver both SMS and WhatsApp.

## Webhooks
The delivery of the SMS/Whatsapp is an API call mad by <PERSON> to Twilio. When a message is delivered or received, <PERSON><PERSON><PERSON> calls the
Front's web hook to update the [conversation](frontapp.md#conversation-synchronisation).
You can find the Webhook's configuration in the [Twiolio's console](https://www.twilio.com/console) (Phone Numbers > Active Numbers).

![Twiolio Webhooks](assets/twilio-webhooks.png)  

## SMS Delivery
The SMS delivery is performed with the [Front SMS API](frontapp.md#sms) and delivered with Twilio. This is done
thanks to a ready-to-use integration of the Front's platform. When the SMS channel is first configured (one shot),
it configures a Webhook on Twilio that will update the Front's channel with a new message.

## Incoming SMS
The incoming SMS are handled by Twilio and forwarded to Front via the Webhooks.

## Authy (not used anymore)
[Authy](https://www.twilio.com/authy is used to verify that a phone number exists and belongs to a user. When a user
performs a valuation, he enters his phone number, and then he receives an SMS with an authentification code that allows
him to access the valuation. You can see this behaviour here in this class `\Kiiz\User\Components\AccountVerification::onVerify()`.
