Mail Presets
============
Kiiz provides a set of predifined e-mail presets that are used by the backend users to quickly send e-mails to the customers.

Configuration
-------------
All the e-mail presets are defined in the `config/prests.php` configuration file. 

```php
<?
return [
    'estimator' => [
        'estimator:full-service-followup-1' => [
            'label' => 'kiiz.estimator::lang.preset.button_estimate_review',
            'status' => [\Kiiz\Estimator\Models\Estimate::STATUS_PENDING, \Kiiz\Estimator\Models\Estimate::STATUS_ESTIMATED],
            'group_id' => \Kiiz\User\Models\UserGroup::GROUP_FULL_ID
        ]
        // ... more presets
    ],
    'property' => [
        // send publication links to the client
        'property:publication' => [
            'label' => 'kiiz.property::lang.action.send_publication',
            'status' => ['published']
        ]
        //... more presets
    ]
];
``` 
The configuration file allows the developer to add a preset depending on the status (see the [Worflow](workflow.md) section)
of the "estimation" or "property". 

Create a preset
---------------
To successfully create a preset, you need to create a class that extends the `\Kiiz\Mailer\Classes\Preset` class and
implement the `getBody()` and `getSubject()` methods that will populate the e-mail. Optionnaly you can implement the 
`onRun($data)` method that will initialize the class with the $_GET parameters of the request.
```php
<?php namespace Kiiz\Estimator\Presets;

class MyPreset extends \Kiiz\Mailer\Classes\Preset
{
    public function onRun($data)
    {
        // initialisation
    }
        
    /**
     * Returns the subject of the email
     * @return mixed
     */
    public function getSubject()
    {
        return trans('kiiz.estimator::lang.preset.valuation.followup1.subject');
    }

    public function getBody()
    {
        return trans('kiiz.estimator::lang.preset.valuation.followup1.body');
    }
}
```

Once the class is created, you need to declare it in your Plugin in the `$presets` protected property.
```php
<?
class Plugin extends \Kiiz\Backend\Classes\Plugin\Plugin
{
    /**
     * Register here all your plugins DataInitializers to prepare the subject and body of an email
     * @var array
     */
    protected $presets = [
        'my-plugin:my-preset' => MyPreset::class
    ];
}

```

Send a preset email directly
------
```
<?
\App::make(Mailer::class)->sendPreset(
    $userId,
    $backendUserId,
    $presetName,
    [
        'property_id' => $propertyId,
        'client' => $userId
    ]
);
```

Send a preset email using a delayed task
------
```
<?
$taskManager->createTask(
    $client,
    "Visit Calendar Link",
    $preset . $client->id,
    '+1 hour',
    null,
    SendPresetMail::class,
    [
        'user_id' => $client->id,
        'backend_user_id' => $backendUserId,
        'preset' => [
            'name' => $presetName,
            'params' => [
                'property_id' => $propertyId,
                'client' => $client->id
            ]
        ]
    ]
);
```

Events
------
Once an e-mail is sent, it will fire the `kiiz.mailer.send` event that can be catched by a custom event listener.  
On that event you have the `\Kiiz\Mailer\Models\Mail` model with the field `extra_data` populated with the `$_GET` params
of the preset.
