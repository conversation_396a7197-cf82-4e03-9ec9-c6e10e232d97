# VueJS
Some of the Backend’s components are written with VueJS to enhance the user experience. You can find them in the
`plugins/kiiz/backend/assets/src/kiiz` directory. The application is bootstrapped in the `app.js` file and the components
 are located within some of the subdirectories. It's higly recommended to try read their documentation and try some tutorials
to full understand how the components works. [Read more](https://vuejs.org/).

Usually, the VueJS components communicate with the Backend through a [REST API](rest-api.md). Make sur to read it.

## Selector
The VueJS component's must be wrapped with an HTML tag containing the classname `vue-app`. You can't put this class
to the body because it conflicts wht October's CSM Javascript Framework.

Here's an example of implementation:

```
<div class="vue-app" data-control="vue-app">
    <div class="task-list-item">
        <task-list-item
            :task="<?= e(json_encode($task)) ?>"
            v-bind:key="<?= $task['id'] ?>"
            v-bind:backend-user-id="<?= \BackendAuth::getUser()->id ?>"
        ></task-list-item>
    </div>
</div>
```
