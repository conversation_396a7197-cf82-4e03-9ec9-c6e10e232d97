Hosting
=======
The platform is hosted with Infomaniak. The developers can log with an SSH account provided by the Lead Developer on demand.
## PHP version
To select the php version to use in CLI, you can set it by editing the `~/.profile` file and modifiy as below
```bash
export PATH=/opt/php7.1/bin:$PATH
```
## File system structure
The hosting is structured with the following directories:
- **prod**: Web application for the production environment
- **preprod**: Web application for the preproduction environment
- **utils/php-cli/php.ini** PHP configuration for batch commands. Each time the `php` command is launched it automatically loads
the content of the php.ini
- **utils/composer/** Composer command line tool. An alias is created to use directly `composer` in the command line
- **backups**: Daily backups. They can also be restored from the Infomaniak Web Console.

## Mail
### Development
All development emails are sent to you `maildev` instance which is run out of the box by docker. It can be accessed via
the url `http://localhost:8282`.
### Preprod & prod
The preprod & prod hosting uses the Infomaniak SMPT servers to send the outgoing emails. All the outgoing emails are
sent in carbon copy to `<EMAIL>` and `<EMAIL>`. This is used to review all emails that are sent.
All configuration is in `{projectRoot}/config/{env}/mail.php`

## Database
- **prod** - me5du_prod
- **preprod** - me5du_prod
To get the host/user/password, please refer to the `keepass` file.

### Production backups
Infomaniak performs one backup each day at 04:00.
On top of that, we have our own backup system `bin/mysql-backup.sh` that is executed each hour.
Be sure that it is added in the crontab of the production server!

The backups are stored in `~/mysql-backups`.

## Crontab
Run `crontab -l` while logged in the Server.
```bash
# and what command to run for the task
#
# To define the time you can provide concrete values for
# minute (m), hour (h), day of month (dom), month (mon),
# and day of week (dow) or use '*' in these fields (for 'any').#
# Notice that tasks will be started based on the cron's system
# daemon's notion of time and timezones.
#
# Output of the crontab jobs (including errors) is sent through
# email to the user the crontab file belongs to (unless redirected).
#
# For example, you can run a backup of all your user accounts
# at 5 a.m every week with:
# 0 5 * * 1 tar -zcf /var/backups/home.tgz /home/
#
# For more information see the manual pages of crontab(5) and cron(8)
#
# m h  dom mon dow   command
* * * * * /opt/php7.1/bin/php /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/preprod/artisan schedule:run >> /dev/null 2>&1
* * * * * /opt/php7.1/bin/php /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/prod/artisan schedule:run >> /dev/null 2>&124 * * * * /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/prod/bin/mysql-backup.sh >> /dev/null 2>&1
5 * * * * /home/<USER>/19272f08c946e2aa0d116796e1a0a8ba/prod/bin/mysql-backup.sh >> /dev/null 2>&1
```

### october cms V3 / new server kiiz-2 (14-01-2024) ###
use Redis infomaniak on preprod and prod.

require:
Installer client predis via composer app octobercms

add file .env

Dev :

REDIS_CLIENT=predis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

preprod /prod

REDIS_CLIENT=predis
REDIS_HOST=localhost
REDIS_PASSWORD=keepass
REDIS_PORT=6379

dev/preprod/prod

CACHE_DRIVER=redis

## Crontab
Run `crontab -l` while logged in the Server.
```bash
# Edit this file to introduce tasks to be run by cron.
#
# Each task to run has to be defined through a single line
# indicating with different fields when the task will be run
# and what command to run for the task
#
# To define the time you can provide concrete values for
# minute (m), hour (h), day of month (dom), month (mon),
# and day of week (dow) or use '*' in these fields (for 'any').
#
# Notice that tasks will be started based on the cron's system
# daemon's notion of time and timezones.
#
# Output of the crontab jobs (including errors) is sent through
# email to the user the crontab file belongs to (unless redirected).
#
# For example, you can run a backup of all your user accounts
# at 5 a.m every week with:
# 0 5 * * 1 tar -zcf /var/backups/home.tgz /home/
#
# For more information see the manual pages of crontab(5) and cron(8)
#
# m h  dom mon dow   command
* * * * * /opt/php8.1/bin/php /home/<USER>/7b6085b5eaf61bba7c34d93d5b7937cd/sites/dev.kiiz.ch/artisan schedule:run >> /dev/null 2>&1
5 * * * * /home/<USER>/7b6085b5eaf61bba7c34d93d5b7937cd/sites/dev.kiiz.ch/bin/mysql-backup.sh >> /dev/null 2>&1
```
