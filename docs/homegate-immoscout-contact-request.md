Publication portals - Injection of contact request
=======

These portals offer a contact form that can be used by people who are interested in a property.
The contact data are sent by <NAME_EMAIL> and injected in [Frontapp](frontapp.md).<br/>
Following the regular Frontapp workflow, a user (`users`) and a contact (`kiiz_user_contact`) are created in October database.<br/>
An event handler (`\Kiiz\Property\EventHandlers\ParseInterestedContact`) is bound to the event `kiiz.frontapp.conversation`
and will initiate the contact parsing (`\Kiiz\Property\Classes\InterestedContact\ParserHandler`).
If a portal parser can be used, based on the From adress of the email, the user is updated with the contact data and an
interested person (`kiiz_property_interested`) is added to the property.<br/>
Finaly, the user receives an automatic email (`\Kiiz\Property\Classes\InterestedContact\Parser\Parser`) containing a link on the visitor calendar, only for ppe and houses (not commercial properties).<br/><br/>
Below are the specificities of each portal parsers.


Homegate / home.ch / RaiffeisenCasa
-----------
The contact form data are sent by email which body contains a JSON object:
```
{
  "languageId": "de",
  "portal": "homegate",
  "client": "desktop",
  "refProperty": "20180730-YGS",
  "refHouse": "20180730-YGS",
  "refObject": "20180730-YGS",
  "offerType": "SALE",
  "objectType": "APPT_DUPLEX",
  "objectNumberRooms": "4",
  "objectFloor": "0",
  "advertisementId": "109362870",
  "advertisementUrl": "https:\/\/test.homegate.ch\/webapp\/kaufen\/109362870",
  "agencyId": "d587",
  "agencyName": "kiiz",
  "agencyStreet": "Case Postale 5772",
  "agencyZip": "1002",
  "agencyCity": "Lausanne",
  "agencyReference": "Nom du responsable des ventes",
  "agencyEmail": "<EMAIL>",
  "inquirySalutation": "MS",
  "inquiryName": "Doe",
  "inquiryFirstname": "Jane",
  "inquiryStreet": "Av. de la Harpe 12",
  "inquiryZip": "1007",
  "inquiryCity": "Lausanne",
  "inquiryCountry": "Switzerland",
  "inquiryEmail": "<EMAIL>",
  "inquiryPhone": "+41781234567",
  "inquiryBirthday": "",
  "inquiryHometown": "",
  "inquiryCivilstatus": "",
  "inquiryNumberpersons": "",
  "inquiryJob": "",
  "inquiryEmployer": "",
  "inquirySalary": "",
  "inquiryViewing": "",
  "remark": "TEST KIIZ"
}
```

This object can then be found in table `kiiz_user_contact > extra_data` in the property `target.data.text`.<br/>
An example of the extra_data object is stored here: `plugins/kiiz/property/tests/fixtures/interested-contact/homegate/extra-data.json`.

Immoscout / Comparis / Anibis
-----------
Deprecated Immoscout since 17.11.2023
The contact form data are sent by email in an XML attachment:
```
<?xml version="1.0" encoding="UTF-8" ?>
<openimmo_feedback>
    <version>1.2.5</version>
    <sender>
        <name>IS24 CH</name>
        <datum>04.11.2018 18:52:01</datum>
        <makler_id>766114</makler_id>
    </sender>
    <objekt>
        <portal_obj_id>5096113</portal_obj_id>
        <oobj_id>20180730-YGS.20180730-YGS.20180730-YGS</oobj_id>
        <expose_url>https://www.immoscout24.ch/de/d/5096113</expose_url>
        <bezeichnung>Charmant appartement traversant avec balcon</bezeichnung>
        <strasse>Rue du Four 3</strasse>
        <ort>Pompaples</ort>
        <stadtbezirk>Rue du Four 3</stadtbezirk>
        <interessent>
            <anrede>Madame</anrede>
            <vorname>Jane</vorname>
            <nachname>Doe</nachname>
            <strasse>Av. de la Harpe 12</strasse>
            <plz>1007</plz>
            <ort>Lausanne</ort>
            <tel>0781234567</tel>
            <email><EMAIL></email>
            <anfrage>
                Bonjour, Veuillez m’envoyer le dossier de vente relatif à cet objet.
            </anfrage>
        </interessent>
    </objekt>
</openimmo_feedback>
```
new format immoscout since 17.11.2023
```
{
"languageId": "de",
"portal": "homegate",
"platform": "browser",
"client": "desktop",
"refProperty": "abc",
"refHouse": "abc",
"refObject": "abc",
"offerType": "RENT",
"objectType": "APPT_APARTMENT",
"objectNumberRooms": 4,
"objectFloor": 5,
"advertisementId": "3000000003",
"advertisementUrl": "https:\/\/test.homegate.ch\/mieten\/3000000003?u_c=tcsl",
"agencyId": "a100",
"agencyName": "ImmoAgent",
"agencyStreet": "Bahnhofstrasse 20",
"agencyZip": "8810",
"agencyCity": "Horgen",
"agencyReference": "",
"agencyEmail": "<EMAIL>",
"inquirySalutation": "",
"inquiryName": "Muster",
"inquiryFirstname": "Hans",
"inquiryStreet": "Im Feld 10",
"inquiryZip": "3005",
"inquiryCity": "Bern",
"inquiryCountry": "",
"inquiryEmail": "<EMAIL>",
"inquiryPhone": "0314344394",
"inquiryBirthday": "",
"inquiryHometown": "",
"inquiryCivilstatus": "",
"inquiryNumberpersons": "",
"inquiryJob": "",
"inquiryEmployer": "",
"inquirySalary": "",
"inquiryViewing": "",
"remark": "Guten Tag\nIch interessiere mich f\u00FCr dieses Objekt.\nFreundliche Gr\u00FCsse"
}
```

This attachment remains in Frontapp and its URL is found in table `kiiz_user_contact > extra_data` in the property `target.data.attachments`.<br/>
An example of the extra_data object is stored here: `plugins/kiiz/property/tests/fixtures/interested-contact/immoscout/extra-data.json`.
