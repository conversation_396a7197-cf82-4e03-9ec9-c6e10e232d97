# Backend helpers
This section will show you some helpfull helpers that are stored in the `Kiiz\Backend` plugin that should be used 
whenever it's possible.
## Shortcut helpers on views
If you need to format data in the view you can use the view helpers defined in `plugins/kiiz/backend/init.php`.  
Some examples :
```php
<span><?= m2($livingSpace) ?></span>
```
## Create a form widget
To create a reusable form widget use the following command
```bash
php artisan create:formwidget Kiiz.Backend MyWidget
```
Also see <https://www.sitepoint.com/building-octobercms-form-field-widgets-like-a-pro/> and
<https://octobercms.com/docs/backend/widgets#form-widgets>

## Http (Rest & Soap)
All external services (API, SMS, etc...) should use these HTTP services.
### Rest
For Rest calls always use the `\Kiiz\Backend\Classes\Http\Rest` class which extends `GuzzleHttp\Client` class which logs every 
request user a `Monolog` logger. Example call :
```php
// create a logger
$log = new Logger($name);
// file handler
$log->pushHandler(new RotatingFileHandler(storage_path('logs/' . $name . '.log'), Logger::WARNING));
$client = new \Kiiz\Backend\Classes\Http\Rest([], $log);
```
This will ensure that every HTTP request is logged into a file.

### Soap
For Soap call always use the `\Kiiz\Backend\Classes\Http\Soap` wich extends the basic SoapClient. It extends it by 
adding a Monolog logger wich will log every Soap request in a specific log file. For example:
```php
// create a logger
$log = new Logger('my-log-file');
// file handler
$log->pushHandler(new RotatingFileHandler(storage_path('logs/' . $name . '.log'), Logger::WARNING));

// creating Soap Client
$soapClient = new Soap($this->config['endpoint'], array(
    'local_cert' => $certFile,
    'passphrase' => $this->config['cert_passphrase'],
    'soap_version' => SOAP_1_1,
    'Trace' => 1,
    'exceptions' => true,
), $log);
```

## UI
### Popups
To display a popup with javascript you can call the following code:
```
const popupTrigger = $(`<a></a>`);
popupTrigger.popup({
    handler: 'onSave', // put here your ajax handler
    extraData: params // custom post parameters
});
```

## Tokens
To generate unique tokens for a model you should have a look on this documentation: https://github.com/vuer/token
### Generating token
You can associate a token with a model like this:
``` php
$user  = User::find(1);
$token = $user->createToken('token_name');
```
To specify own expiration time or token length you should add second and third parameters:
``` php
// 180 minutes, 100 characters.
$token = $user->createToken('token_name', 180, 100);
```
You can also pass an array with custom properties:
``` php
$token = $user->createToken('token_name', 180, 100, ['email' => '<EMAIL>']);
```
To check token you can use **checkToken** method:
``` php
if ($user->checkToken('tc0kml61DT3t6xciInw7gjqwmfvZ2799max7lMMGWl2yL9TB')) {
    // token is valid
}
```
You can also delete token:
``` php
// Delete token by name.
$user->deleteToken('token_name');

// Delete token by value.
$user->deleteToken('tc0kml61DT3t6xciInw7gjqwmfvZ2799max7lMMGWl2yL9TB', 'token');
```
To get Token instance use methods:
``` php
// Get one token by name.
$user->getToken('token_name');

// Get collection of tokens by name.
$user->getTokens('token_name');

// Get one token by value.
$user->getToken('tc0kml61DT3t6xciInw7gjqwmfvZ2799max7lMMGWl2yL9TB', 'token');

// Get collection of tokens by value.
$user->getTokens('tc0kml61DT3t6xciInw7gjqwmfvZ2799max7lMMGWl2yL9TB', 'token');
```
