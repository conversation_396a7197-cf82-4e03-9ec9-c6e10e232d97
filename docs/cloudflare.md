# Cloudflare
Cloudflare secures and ensures the reliability of your external-facing resources such as websites, APIs, and applications.
It protects your internal resources such as behind-the-firewall applications, teams, and devices. And it is your platform for developing globally scalable applications.

## DNS configuration
The DNS entries of www.kiiz.ch are defined in the Cloudflare's Dashboard (not on Infomaniak). It looks to something like this:

![SMS Settings](assets/cloudflare-dns.png)  

## Cache
Cloudflare will automatically cache (for 4 hours) every static asset (js, css, images, pdf, etc...) which is powerful and easy to maintain.
With this flexibility comes some drawbacks that you need to keep in mind when releasing a new version.
1. On each release, you need to manually update the variable `$this['resourcesVersion']` version defined in:
 - `themes/kiiz/layouts/layout.htm`
 - `themes/kiiz/layouts/dashboard.htm`
 - Additionally, \Kiiz\Backend\Plugin::addAssetsToBackendController increment the value of `$version`.
   
> You can manually purge all the cache via the Cloudflare's console if you encounter issues.

## File upload max site
The maximum upload file can be 100MB, keep this in mind if the server returns an error while uploading large files from 
the backend.
