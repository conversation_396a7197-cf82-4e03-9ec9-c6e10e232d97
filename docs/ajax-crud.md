AJAX CRUDS
==========
To implement ajax CRUD in a controller add the following behaviour :
```
public $implement = ['<PERSON><PERSON>\Backend\Behaviors\AjaxFormController'];
```
This will add extra methods that handles the display of an October CMS form in a popup.

- onAjaxPreview
- onAjaxUpdate
- onAjaxCreate

Then you can add `a` tag with the following parameters to show it:

```
<a
    data-control="popup"
    data-request-url="/some-url"
    data-handler="onAjaxPreview"
    data-extra-data="id: 1, onsave: 'your.custom.event'"
    href="javascript:;"
    class="btn btn-primary btn-lg">
    Launch Ajax Form
</a>
```
The `data-request-url` is optional. If not defined, the current's page handler will be used.  
The `data-extra-data` contains post params that will populate the form. The `onsave` will trigger a custom event when the
form is submited, so you can catch this event on your page and perform a custom action.
```javascript
// custom event that will be triggered when the popup is submitted
$(window).on('your.custom.event', function () {
    console.log('submited!');
});
```
### Opening popup via javascript
```javascript
window.Kiiz.openPopup(
    'http://localhost/backend/kiiz/user/users/preview/98',
    'onAjaxUpdate',
     {id: 98}
 );
```
## Form configuration
On the form configuration `config_form.yaml` you may specifify custom actions to show in the form:

```yaml
name: Mails
form: $/kiiz/mailer/models/mail/fields.yaml
modelClass: Kiiz\Mailer\Models\Mail
defaultRedirect: kiiz/mailer/mails
create:
    redirect: 'kiiz/mailer/mails/update/:id'
    redirectClose: kiiz/mailer/mails
    # custom action buttons for AJAX CRUD
    actions:
        send:
            label: 'kiiz.mailer::lang.label.send'
            dataHandler: onSend
update:
    # custom confirm message before deletion 
    deleteConfirm: 'kiiz.calendar::lang.event.remove_visit_confirm'            
    # custom option to disable the delete button (enabled by default) 
    deleteEnabled: 'kiiz.calendar::lang.event.remove_visit_confirm'            
```
