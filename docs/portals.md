Publication Portals
===================
The properties that are stored in the backend, once published, are synchronized with the principal Real Estate Portals
of Switzerland. We've developed so far two connectors (homegate.ch and immoscout.ch) that relay the properties to
more portals (Home.ch, anibis.ch and comparis.ch).

## Portal relays
- Homegate
    - Home.ch
- Immoscout 24
    - Anibis
	- Comparis

## Preproduction
The only portal that offers a preproduction environment is homegate.ch
https://test.homegate.ch/louer-ou-acheter/biens-immobiliers/liste-annonces?tab=list&a=d587&incsubs=1&l=neutral

!> Homegate.ch have an IP filter for incoming requests, if you have issues accessing this from the office, contact
<EMAIL> and request them to add you office's IP.

## FTP synchronisation (Cron)
The properties are pushed every 15 minutes by FTP to the homegate.ch and immoscout.ch servers using the IDX format file.
> Make sure to read their documentation before assuming or making any changes to the code.
[IDX Definition](modules/portals/idx-format-v3.01_v018.xls ':ignore')

The published properties (`is_published` flag) are written into a CSV file (IDX format) with their information (square meters,
city, etc...) and the path to the images in the FTP server. Then we push this file to the FTP server along with the property's
images.

## Code Architecture
The entrance point of the `Published` is in `\Kiiz\Publisher\Classes\Publicators\Publisher::publish()`. It registers
the `Publicators` which are responsible of pushing the properties to a specific portal (immoscout.ch or homegate.ch).
Each publicator stores a `yaml` configuration that defines the mapping between the `Property` model and the IDX definition.

## YAML mapping
Each publicator has it's own `plugins/kiiz/publisher/classes/publicators/{publicator}/config.yaml` configuration that
describes the field mapping between the model and the IDX file.

```yaml
object_street: # IDX column name
    from: street|street_number #field name from the model
    rules: "sometimes|nullable|string|max:200" # rules for the property to be published
    fieldNumber: 8 # field number related to the IDX file
    transformer: # used to transform the model's data to the expected idx format.
        name: street
```
> If a property's data doesen't satisfy the rules defined in the `yaml` definition, then a warning is shown in the backend
> a callout message. This is to warn the backend user that something is wrong and that an action should be taken.
