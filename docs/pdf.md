# PDF
We use Dompdf (https://github.com/dompdf/dompdf) and the Laravel wrapper (https://github.com/barryvdh/laravel-dompdf) to 
convert HTML into PDF files.

## Kiiz PDF generator
To generate your own PDF, create a class that extends `Kiiz\Core\Classes\Pdf\Generator` and define the following:
- $layoutView
- $partialView
- $styleSheets
- init()
- filename()

Optionally you can also set these configuration:
- $paperFormat (a4, a3, ...)
- $paperOrientation (portrait, landscape)
