# Forms
We have extended the default October Form Builder with `\Kiiz\Backend\Helpers\FormBuilder` which implements materialize
(Material Design) wrappers for default form components.

## Ajax Form
To open an Ajax Form use the following twig statement:
```
{{ form_ajax({'url': 'foo/bar', 'method': 'post'}, {'data-request-validate': true}) }}
```
## Form Macros
The following macros are available at the moment:
### wrapInput
```
{{ wrap_input('input_type', 'my_field_name', 'my label', my_value|null, {'comment': 'my helper text'}) }}
```
The id of the input is produced from the field name if not given in options `{id: "myId"}`

If you need to set the field as required, you can add the option `{required: true}`

If you need to give the field extra css class, you can add the option `{wrapperClass: "css more-css css-class"}`

This wrapper will produce a input bloc with the label and a materialize container:
```
<div class="input-field required [wrapperClass]">
    <input type="[input_type]" name="[my_field_name]" value="[my_value]" />
    <label for="[id]">trans()</label>
    <span data-validate-for="[name] class="helper-text red-text"></span>
    <!-- Optional -->
    <span class="helper-text">[comment_options]</span>
</div>
```
You can also pass translation keys as content.
### wrapSelect
```
{{ wrap_select(fieldName, field.label, ['1': 'Option1'], null, {'comment': 'my comment'}) }}
```
The label and helper text accept translation keys as well.
