How to install an October Plugin
============
In order to add a new plugin in the project, we have to require the dependency with composer. There are 2 available methods.
## Plugin has composer.json
If you are lucky, the plugin has a `composer.json` file in GitHub and the installation is quite simple :
```
composer require rainlab/user-plugin
```
As the plugin is typed as an october-plugin in the composer.json file, it will be automatically added to the plugins folder.

## Plugin doesen't have a `composer.json` 
When a plugin doesen't have a `composer.json` file in the GitHub repository, you have to manually add the repository to the
project's `composer.json` file.
```json
"repositories": [
    {
        "type":"package",
        "package": {
            "name": "excodus/oc-translateextended",
            "version":"master",
            "type": "october-plugin",
            "source": {
                "url": "https://github.com/Excodus/oc-translate-extended.git",
                "type": "git",
                "reference":"master"
            }
        }
    }
],
```
Remove any dash on the `package.name` property to avoid problems.
Then you have to tell composer that the file will be stored in the `plugins/*` directory. To do so you have to add a new 
line to the `installer-`
```json
"extra": {
        "merge-plugin": {
            "include": [
                "plugins/*/*/composer.json"
            ],
            "recurse": true,
            "replace": false,
            "merge-dev": false
        },
        "installer-paths": {
            "plugins/excodus/{$name}": ["excodus/oc-translateextended"]
        }
    }
```

Then run the below command to install the package:
```bash
composer require excodus/oc-translateextended
```
This action will also update the `composer.lock` file.

Updating October CMS
---------------------
To update the version of October CMS you need to run the following commands, on each environment.
```bash
composer update october/* --no-dev
composer update mohsin/rest
php artisan october:up
```
