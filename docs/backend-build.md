# Backend Javascript/CSS Compilation

You need to be in the backend's assets directory in order to run any command :
```
cd plugins/kiiz/backend/assets/
```

If it's the first time you have to install all the npm dependencies :
```
npm install
```

## Building for production or development install
```
npm run build
```

## Working in development
When you want to edit JS or CSS files, you should run the below command ([see Useful bash aliases](dev-quick-start.md)) so the code can be autogenerated
```
gulp-backend
```
