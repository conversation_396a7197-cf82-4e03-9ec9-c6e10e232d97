Dropbox
=======
Ki<PERSON> has a Business Dropbox account which is used to store the clients documents. In order to automate the saving of the
property owner's documents, we use the Dropbox's API to push all the e-mail's attachments into the shared path
`/kiiz team/CLIENTS/FOR SALE`.

We use a combination Front App (mail injection) and Dropbox to perform this task. Only the attachments of the customers
having a selling property are stored in Dropbox, the other e-mail attachment are ignored.

Dev Account
-----------
The calls made to the API use the <PERSON>' Dropbox account access token. Please see the keypass to get the access.
To manage the Dropbox application click [here](https://www.dropbox.com/developers/apps/info/iq3o17t42daeb8v).

API
---
Here are some usefull links to get involved with the Dropbox's API
 - [API Docs](https://www.dropbox.com/developers/documentation/http/documentation) All the API calls are documented
 - [Namespace Guide](https://www.dropbox.com/developers/reference/namespace-guide) How to upload files into a shared
 folder with a Business account.
 - [API Explorer](https://dropbox.github.io/dropbox-api-v2-explorer) Test your API calls here. Warning, the namespaces
 not available yet with this tool, so you won't be able to push files into a shared folder.

Shared Folders
--------------
Each folder has a `shared_folder_id` which is used to identify the folders when uploading files.
`kiiz team` (root folder): shared_folder_id: **********

When accessing shared folders, the header `'Dropbox-API-Path-Root: {".tag": "namespace_id", "namespace_id": "**********"}'`
must be specified.

File naming
-----------
The uploaded files are always under the `/kiiz team/CLIENTS/FOR SALE` folder. The individual naming of a file is the
following:

Filename: "test.txt"
Client:
 - Name: Joe Doe

Property:
 - city: Pompaples
 - slug: 20181102_JGF

```
/kiiz team/CLIENTS/FOR SALE/Doe_Pompaples_20181102_JGF/Mail_Attachments_20181102_JGF/test_20181102_JGF.txt
```

How to use
----------
Here's an example on how to upload a file via Dropbox
```php
<?
//Configure Dropbox service
$dropbox = \App::make(\Kiiz\Dropbox\Classes\Dropbox::class);
$uploadedFile = $dropbox->uploadContent('My file content!', "/MY_DROPBOX_PATH/file.txt");
```

## Update 01.11.2023

long live token is over.
### Generate a new acces code.
- create a new application from dropbox (dropbox console )https://www.dropbox.com/developers/apps?_tk=pilot_lp&_ad=topbar4&_camp=myapps
- Get acces code. linker your application with cms. From browser, enter https://www.dropbox.com/oauth2/authorize?client_id=<APP_KEY>&token_access_type=offline&response_type=code
- Warning the code expire less 2 minutes
- Get the refresh token :
  methode post
  curl https://api.dropbox.com/oauth2/token \
  -d code=<AUTHORIZATION_CODE> \
  -d grant_type=authorization_code \
  -d client_id=<APP_KEY> \
  -d client_secret=<APP_SECRET>
- update the refresh token :
methode post
  curl https://api.dropbox.com/oauth2/token \
  -d grant_type=refresh_token \
  -d refresh_token=<REFRESH_TOKEN> \
  -d client_id=<APP_KEY> \
  -d client_secret=<APP_SECRET>
  -config/dropbox insert client_id, client_secret and refresh_token

==> https://www.dropbox.com/developers/documentation/http/documentation
