# kiiz
Kiiz SA is a real estate platform that disrupts the current Swiss Market. It drastically reduces the cost of a house selling with a fixed priced, not a commission like the usual broker’s pricing.
This project relies on a broad set of third party technology services to achieve specific tasks like the property’s valuation, SMS communication with the customer, email communication and many more. This document gives an overview on how these services interact with the CRM.

### Developer quick start
To install the kiiz project on your PC, you can go through this guide that explains step by step how to install a development environment.
[Get Started now](dev-quick-start.md).

### October CMS
The kiiz's backend is built with October CMS which is a superset of Laravel (PHP). It’s highly recommended to watch their video tutorials and read the documentation to fully understand the project’s structure. 


[October](http://octobercms.com) is a Content Management System (CMS) and web platform whose sole purpose is to make your development workflow simple again. It was born out of frustration with existing systems. We feel building websites has become a convoluted and confusing process that leaves developers unsatisfied. We want to turn you around to the simpler side and get back to basics.

October's mission is to show the world that web development is not rocket science.

[![Build Status](https://travis-ci.org/octobercms/october.svg?branch=develop)](https://travis-ci.org/octobercms/october)
[![License](https://poser.pugx.org/october/october/license.svg)](https://packagist.org/packages/october/october)

### Learning October

The best place to learn October is by [reading the documentation](http://octobercms.com/docs) or [following some tutorials](http://octobercms.com/support/articles/tutorials).

You may also watch these introductory videos for [beginners](https://vimeo.com/79963873) and [advanced users](https://vimeo.com/172202661).


Please follow the following guides and code standards:

* [PSR 4 Coding Standards](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-4-autoloader.md)
* [PSR 2 Coding Style Guide](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md)
* [PSR 1 Coding Standards](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-1-basic-coding-standard.md)
