Kiiz plugins
================
The default October CMS [plugin system](https://octobercms.com/docs/plugin/registration) is extended to enhance the declaration of services, event handlers and console
commands. Every kiiz plugin should extend the `\Kiiz\Backend\Classes\Plugin\Plugin` class. It results in a more readable 
and easy to extend plugin.

## Registrable features
The registrable features are single classes that initialize an IoC container, an alias or external service providers 
that needs to be initialized in the beginning of the application during the registering of a plugin. Registrable features are initialized in the `protected $register` 
property of the plugin.
```php
/**
 * Add here all the classes that should be registered when the plugin is first registered. For example IoC container
 * initialisation
 * @var Registrable[]
 */
protected $register = [
    Register\Workflow::class,
    Register\TwilioClient::class,
    // ...more registerables here
];
```

A registrable class must implement the `\Kiiz\Backend\Classes\Plugin\Registrable` interface and implement an `init()` method.
```php
class MyCustomRegistrable implements \Kiiz\Backend\Classes\Plugin\Registrable
{
    /**
     * Initializes the feature.
     * @throws \Exception
     */
    public function init()
    {
        // implementation
    }
}
```

The `Registrable` class implementations are stored in the `/plugins/kiiz/{pluginName}/register` directory.

## Bootable features
Same as registrable features but initialized during the booting of a plugin.
Bootable features are initialized in the `protected $boot` property of the plugin 

## Event Listeners
The event listeners are a key part of the kiiz project. The main philosophy is to create light weight business class which
fire `events` that can be catched by any other kiiz plugin, extending the features and not modifying them, which often lead
to bugs.

You can declare the events of your plugin in the `protected $eventListeners` property.

```php
/**
 * Plugin's event listeners
 * @var array
 */
protected $eventListeners = [
    'workflow.completed' => OnTransitionComplete::class,
    // fired before sending the email
    'mailer.prepareSend' => [
        // prepends the environment to the subject for dev & preprod only
        MailPrependEnvironment::class,
        // adds a carbon copy email address to all outgoing emails
        MailAddCarbonCopy::class
    ],
    // disables/enables fields depending on the user's role
    'backend.form.extendFields' => FormFieldPermissions::class
];
```
The `key` must contain the name of the event, and the `value` can contain an 
[event handler class](https://octobercms.com/docs/services/events) or an array of event handlers.

If you add or modify a new event handler, make shoud to add a comment line explaining what the event does.

The event handlers class implementations are stored in the `/plugins/kiiz/{pluginName}/eventhandlers` directory.

## Console Commands
To register console commands in your plugin, add the class names in the `protected $consoleCommands` property.

```php
/**
 * Add here all your custom console commands
 * @var array
 */
protected $consoleCommands = [
    'db.drop-tables' => Console\DropTables::class
];
```
To create a console command read the [October CMS documentation](https://octobercms.com/docs/console/development).

The Console commands class implementations are stored in the `/plugins/kiiz/{pluginName}/console` directory.

## Service Providers
The third party [Laravel's Service Providers](https://laravel.com/docs/5.5/providers) can be added to the plugin's `protected $serviceProviders`
property.
```php
/**
 * Add here all your Plugin's third party service providers
 * @var array
 */
protected $serviceProviders = [TokenServiceProvider::class];
```
That's it, easy peasy!
