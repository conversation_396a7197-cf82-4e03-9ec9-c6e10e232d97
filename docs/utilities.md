Introduction
============
This document gives an overview of the Kiiz plugins.

## Backend
This Plugin should extend the Backend module to add custom behaviour to the October CMS Backend module and layout (CSS/JS).
It may also add global helpers or filtres to the whole application.

### Add a custom JS/CSS file to all the Backend pages
Go to `plugins/kiiz/backend/Plugin.php` and add your file to the `boot()` method.
```php
// add here all global assets to include in your project
\Event::listen('backend.page.beforeDisplay', function ($controller) {
    $controller->addJs('/plugins/kiiz/backend/assets/js/form-errors.js');
});
```
### Translations
The default Backend Translator is improved to handle also `Rainlab Translate` database translations. This behaviour is 
implemented in the `plugins/kiiz/backend/Plugin.php`.  
If the translation is not found in the database, there's a fallback to the default Backend Translator which handles php 
array based strings.

#### Logging used translations
It can be usefefull to log in a CSV file all the used translations to avoid having unused ones in the Nutella Translations
file. To do so, you have to edit the file `config/translator.php` and enable the `logUsed` flag. Also, you must add the 
following code in the beginning of the `\RainLab\Translate\Models\Message::trans` method as it can't be extended.
```php
// dev only
if (\Config::get('translator.logUsed')) {
    $usedLogger = \App::make(TranslatedLogger::class);
    $usedLogger->log($messageId);
}
```
After enabling the log, you should visit all the Website and perform "Full Package" and "Valuation" process
### Enums
The dropdown can use the `Enum` module to enhance list managment. There's a special module in the Backend to modifiy 
current content : Admin > Enums. 
#### Forms
You can map a backend dropdown to any enum by setting in the `options` propert the name of the enum prefixed with `list`.
It has to be camel case. For instance, if your enum's name is `regions`, the following field definition should be used :
```yaml
region:
    label: 'Regions'
    type: dropdown
    options: listRegion
```
#### Migrations
If you want to add a new enum or a new item to an enum, you must create a migration file
that extends the `\Kiiz\Backend\Classes\Enum\EnumSeeder` class and set to path to the enum JSON file. Example :
```php
use Kiiz\Backend\Classes\Enum\EnumSeeder;
use Seeder;

class SeedRegions extends EnumSeeder
{
    protected $truncate = false; // set this to true to rebuild the list
    protected $enumFile = 'kiiz/backend/updates/regions.json';
}
```
The `json` file must be in the following format :
```json
{
    "region": {
        "name": "region",
        "items": [
            {"value": "AG", "label_fr": "Argovie", "label_de": "Aargau"},
            {"value": "AI", "label_fr": "Appenzell Rhodes-Intérieures", "label_de": "Appenzell Innerrhoden"},
        ]
    }
}
```
