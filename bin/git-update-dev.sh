#!/usr/bin/env bash

# Fetch and prune all branches
git fetch origin --progress --prune

# Merge modifications into current branch
CURRENT_BRANCH="$(git name-rev --name-only HEAD)"
git merge origin/$CURRENT_BRANCH -v

# Retrieve the list of local branches that no longer exist on origin and delete them
LOCAL_GONE_BRANCHES="$(git branch -vv | grep 'origin/.*: gone]')"
if [[ $LOCAL_GONE_BRANCHES ]]
then
    git branch -vv | grep 'origin/.*: gone]' | awk '{print $1}' | xargs git branch -D
fi
