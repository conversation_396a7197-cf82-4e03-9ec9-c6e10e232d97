#!/usr/bin/env bash
#### Detect environment
#BASE_DIR=$(dirname $(cd $(dirname $0) && pwd))
#cd ${BASE_DIR}

#-----------------------------------------------
# Script Deploy
#-----------------------------------------------

#### Update code version
#git pull
#/opt/php7.3/bin/php ~/utils/composer/composer.phar install --no-dev
#/opt/php7.3/bin/php artisan october:up

# install

curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash
source ~/.bashrc
nvm install 12.22.7

#### Empty caches
#/opt/php7.3/bin/php artisan cache:clear
php artisan cache:clear
#/opt/php7.3/bin/php artisan view:clear
php artisan view:clear

### Killing queue processes
#pkill -f "artisan queue:work"

#### Move to backend and compile JS/CSS files
cd ./plugins/kiiz/backend/assets
npm install
npm run build
cd ../../../..

#### Move to frontapp widget and publish
cd ./plugins/kiiz/frontapp/assets
npm install
npm run build
cd ../../../..

#### Move to theme and publish
cd ./themes/kiiz
npm install
npm run build
cd ../..
