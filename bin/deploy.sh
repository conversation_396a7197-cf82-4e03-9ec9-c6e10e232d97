#!/usr/bin/env bash
#### Detect environment
BASE_DIR=$(pwd)
cd ${BASE_DIR}

#-----------------------------------------------
# Script Deploy
#-----------------------------------------------
#### execute to root project #####
### Update code version ###
### to have access token to make the pull #####
git pull
composer install --no-dev
/opt/php8.1/bin/php artisan october:migrate

#### Empty caches
/opt/php8.1/bin/php artisan cache:clear
/opt/php8.1/bin/php artisan view:clear

### Killing queue processes
pkill -f "artisan queue:work"

#### Move to backend and compile JS/CSS files
cd ./plugins/kiiz/backend/assets
npm install
npm run build
cd ../../../..

#### Move to frontapp widget and publish
cd ./plugins/kiiz/frontapp/assets
npm install
npm run build
cd ${BASE_DIR}

#### Move to theme and publish
cd ./themes/kiiz
npm install
npm run build
cd ${BASE_DIR}
