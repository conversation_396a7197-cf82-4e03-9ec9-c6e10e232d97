#!/usr/bin/env bash

WWW_DIR='/home/<USER>/7b6085b5eaf61bba7c34d93d5b7937cd/sites/prod.kiiz.ch'
DB_CONFIG_FILE=${WWW_DIR}'/.env'
BACKUP_DIR='mysql-backups'
BACKUP_FILE=`date '+%Y%m%d_%H%M%S'`.sql.gz
FILES_TO_KEEP=48

# Extract DB host, user, password and db name from config file
if [[ ! -f "${DB_CONFIG_FILE}" ]]; then
    echo 'Config file not found:' ${DB_CONFIG_FILE}
    exit 1
fi

# Search for the line which contains the "password" string, split it on ' character and then returns the NF-1 result
DB_HOST=$(awk -F '=' '{print $2}' <<< $(sed -n '/DB_HOST=/p' ${DB_CONFIG_FILE}))
DB_USER=$(awk -F '=' '{print $2}' <<< $(sed -n '/DB_USERNAME=/p' ${DB_CONFIG_FILE}))
DB_PASSWORD=$(awk -F '=' '{print $2}' <<< $(sed -n '/DB_PASSWORD=/p' ${DB_CONFIG_FILE}))
DB_NAME=$(awk -F '=' '{print $2}' <<< $(sed -n '/DB_DATABASE=/p' ${DB_CONFIG_FILE}))

if [[ -z "${DB_HOST}" ]] || [[ -z "${DB_USER}" ]] || [[ -z "${DB_PASSWORD}" ]] || [[ -z "${DB_NAME}" ]]; then
    echo 'DB parameters missing'
    exit 1
fi

# Go to home directory
cd ~
# Create and go to backup directory
if [[ ! -d "${BACKUP_DIR}" ]]; then
    mkdir ${BACKUP_DIR}
    if [[ ! -d "$BACKUP_DIR" ]]; then
        exit 1
    fi
fi
cd ${BACKUP_DIR}

# Dump and compress database
# Options:
#   MYSQL_PWD=${DB_PASSWORD} => password not visible in the process list
#   --lock-tables=FALSE --add-locks=FALSE => no lock on tables
#   --single-transaction => ensure the exported data are consistent
MYSQL_PWD=${DB_PASSWORD} mysqldump -h ${DB_HOST} -u ${DB_USER} --single-transaction --lock-tables=FALSE --add-locks=FALSE --ignore-table=${DB_NAME}.system_event_logs ${DB_NAME} --extended-insert=TRUE | gzip --best > "${DB_NAME}_${BACKUP_FILE}"

# Remove old files
if (( $(ls -1 | wc -l) > ${FILES_TO_KEEP} )); then
    ls -r | tail -n +$((FILES_TO_KEEP+1)) | xargs rm
fi
