#!/usr/bin/env bash
#### Detect environment
BASE_DIR=$(dirname $(cd $(dirname $0) && pwd))
cd ${BASE_DIR}

#-----------------------------------------------
# Script set Directory access
#-----------------------------------------------

# Color variables
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Warning for dev
printf "${YELLOW}For dev environment 'use set-permissions.sh dev'${NC}\n"

### List of writable directories, for dev we allow writing on plugins dir
if [ "$1" == "dev" ]; then
    WRITABLE_RECURSIVE_PATH=(
        "/plugins"
        "/storage"
        "/themes"
    )
else
    # preprod and prod permissions only for storage
    WRITABLE_RECURSIVE_PATH=(
        "/storage"
    )
fi

WRITABLE_PATH=(
)

#### Detect environment values
# Users running php scripts
read -r -a WWW_RUN_USERS      <<< $(ps aux|grep -E '[p]hp-fpm|[a]pache|[h]ttpd|[_]www|[w]ww-data'|grep -v root|cut -d' ' -f1|uniq)
if [ -z ${WWW_RUN_USERS} ]; then
    read -r -a WWW_RUN_USERS  <<< $(getent passwd www-data apache wwwrun|cut -d: -f1|uniq)
fi
# Users accessing to www access
read -r -a WWW_USERS          <<< $(ps aux|grep -E '[p]hp-fpm|[a]pache|[h]ttpd|[_]www|[w]ww-data|[n]ginx'|grep -v root|cut -d' ' -f1|uniq)
if [ -z ${WWW_USERS} ]; then
    read -r -a WWW_USERS      <<< $(getent passwd www-data apache wwwrun nginx|cut -d: -f1|uniq)
fi
CURRENT_USER=$(id -un)

#### Tools
set_recursive_permissions () {
    echo "Set user ${1} write access recursively to ${2}"
    # Linux style
    setfacl -Rm "u:${1}:rwX,d:u:${1}:rwX" $2 > /dev/null 2>&1
    # Mac style
    chmod +R +a "${1} allow delete,write,append,file_inherit,directory_inherit" $2 > /dev/null 2>&1
}
set_permissions () {
    echo "Set user ${1} write access to ${2}"
    # Linux style
    setfacl -m "u:${1}:rwX,d:u:${1}:rwX" $2 > /dev/null 2>&1
    # Mac style
    chmod +a "${1} allow delete,write,append" $2 > /dev/null 2>&1
}
set_recursive_read_permissions () {
    echo "Set user ${1} read access recursively to ${2}"
    # Linux style
    setfacl -Rm "u:${1}:rX,d:u:${1}:rX" $2 > /dev/null 2>&1
    # Mac style
    chmod -R +a "${1} allow delete,append" $2 > /dev/null 2>&1
}
set_read_permissions () {
    echo "Set user ${1} read access to ${2}"
    # Linux style
    setfacl -m "u:${1}:rX,d:u:${1}:rX" $2 > /dev/null 2>&1
    # Mac style
    chmod +a "${1} allow delete,append" $2 > /dev/null 2>&1
}

#### Set Acls

# Set full right for you
set_recursive_permissions ${CURRENT_USER} ${BASE_DIR}

for user in "${WWW_USERS[@]}"; do
    set_recursive_read_permissions ${user} ${BASE_DIR}
done
for user in "${WWW_RUN_USERS[@]}"; do
    set_recursive_read_permissions ${user} ${BASE_DIR}
done

for path in "${WRITABLE_RECURSIVE_PATH[@]}"; do
    pathFull="${BASE_DIR}${path}"
    # adding groups
    sudo chown -R ${CURRENT_USER}:www-data ${pathFull}
    sudo chmod -R g+s ${pathFull}
    for path in ${pathFull}; do
        if [ -e ${path} ]; then
            # Set right for apache user

            for user in "${WWW_RUN_USERS[@]}"; do
                set_recursive_permissions ${user} ${path}


            done
        fi
	done
done

for path in "${WRITABLE_PATH[@]}"; do
    pathFull="${BASE_DIR}${path}"
    for path in ${pathFull}; do
        if [ -e ${path} ]; then
            # Set right for apache user

            for user in "${WWW_RUN_USERS[@]}"; do
                set_permissions ${user} ${path}
            done
        fi
	done
done
