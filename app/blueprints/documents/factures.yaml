uuid: 33d88368-4568-419a-ad1c-73c38df8f5e8
handle: Documents\Factures
type: global
name: Factures
drafts: false
multisite: sync
description: Personnalisez vos factures

navigation:
    icon: octo-icon-qrcode
    parent: settings
    order: 2

fields:
    field_pdf_facture:
        label: Facture
        type: richeditor
        span: full
        tab: PDF
        comment: Les placeholders sont {{nom|raw}}, {{adresse}}, {{code_postal}}, {{ville}}, {{date}}, {{numero_de_facture}}, {{texte_standard_ou_valais}}
        toolbarButtons: paragraphFormat|bold|italic|underline|insertLineBreak|align|formatOL|formatUL|insertTable|insertPageLink|insertImage|insertHR|fullscreen|html
        size: huge
    field_pdf_facture_text_standard:
        label: Texte standard
        type: text
        span: left
        tab: PDF
    field_pdf_facture_text_region_vs:
        label: Texte non-standard (Valais, Jura, ...)
        type: text
        span: right
        tab: PDF
    field_pdf_facture_text_region_code:
        label: Cantons pour le texte non-standard
        optionsMethod: Kiiz\Backend\Helpers\Enum::getRegions
        emptyOption: -- Choisir --
        type: checkboxlist
        span: full
        tab: Cantons (Texte non-standard)
    field_pdf_facture_cc_recipients:
        label: Ajouter en copie (CC)
        type: text
        span: left
        tab: PDF
        comment: Veuillez séparer les adresses e-mail par une virgule si vous souhaitez envoyer votre message à plusieurs destinataires.
    field_pdf_facture_assign_user:
        label: Attribution Frontapp
        type: recordfinder
        list: ~/modules/backend/models/user/columns.yaml
        recordsPerPage: 10
        title: Find Record
        keyFrom: id
        nameFrom: full_name
        searchMode: all
        useRelation: false
        modelClass: Backend\Models\User
        tab: PDF
        span: right
        translatable: false
    field_pdf_facture_name:
        label: Nom
        type: text
        span: left
        tab: QR Code
        required: true
    field_pdf_facture_address_line_1:
        label: Address ligne 1
        type: text
        span: right
        tab: QR Code
    field_pdf_facture_pays:
        label: Pays
        type: text
        span: left
        tab: QR Code
        required: true
    field_pdf_facture_address_line_2:
        label: Address ligne 2
        type: text
        span: right
        tab: QR Code
        required: true
    field_pdf_facture_iban:
        label: IBAN
        type: text
        span: left
        tab: QR Code
        required: true
    field_pdf_facture_currency:
        label: Monnaie
        type: text
        span: right
        tab: QR Code
        default: CHF
        required: true
    field_pdf_facture_bank_id:
        label: ID de la Banque
        type: text
        span: left
        tab: QR Code
    field_pdf_facture_total_amount:
        label: Montant Total
        type: text
        span: right
        tab: QR Code
        required: true
    field_pdf_facture_more_info:
        label: Informations Supplémentaires
        type: text
        span: full
        tab: QR Code
