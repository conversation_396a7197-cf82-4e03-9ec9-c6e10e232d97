uuid: 698afed6-70f1-47dd-9dcf-dc2a0f903ff2
handle: Content\Page
type: structure
name: Pages
drafts: true
multisite: true
pagefinder: item

primaryNavigation:
    label: Pages & Menus
    icon: icon-file
    order: 140

navigation:
    icon: icon-file
    parent: Content\Page
    order: 1

structure:
    maxDepth: 1

customMessages:
    buttonCreate: Nouvelle page

groups:
    builder:
        name: Page dynamique
        fields:
            field_builder:
                tab: Modifier
                type: mixin
                name: Builder
                source: Content\Builder
            field_content_fields:
                tab: Contenu
                type: mixin
                name: Content Fields
                source: Content\Page\ContentFields
            field_seo_fields:
                tab: Gérer
                type: mixin
                name: SEO Fields
                source: Content\Page\SeoFields
    simple:
        name: Page simple
        fields:
            field_content:
                label: Contenu
                tab: Modifier
                type: richeditor
                span: adaptive
                column: invisible
                toolbarButtons: paragraphFormat|inlineClass|bold|italic|underline|insertLineBreak|align|formatOL|formatUL|insertTable|insertPageLink|insertImage|insertHR|fullscreen|html
            field_content_fields:
                tab: Contenu
                type: mixin
                name: Content Fields
                source: Content\Page\ContentFields
            field_left_sidebar:
                label: Afficher la barre latérale gauche
                commentAbove: Si coché, le titre H2 sera affiché du côté gauche de cette page.
                type: switch
                span: full
                scope: false
                column: invisible
                tab: Contenu
            field_seo_fields:
                tab: Seo
                type: mixin
                name: SEO Fields
                source: Content\Page\SeoFields

