uuid: 4b0ff095-f898-40e0-a863-0086150034a1
handle: Content\Page\Article
type: mixin
name: Article Fields

fields:
    field_teaser_description:
        label: Description
        type: richeditor
        toolbarButtons: paragraphFormat|inlineClass|bold|italic|underline|insertLineBreak|align|formatOL|formatUL|insertTable|insertPageLink|insertImage|insertHR|fullscreen|html
        span: left
        column: invisible
        tab: Teaser
        required: true
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_hide_date_before_first_element:
        type: switch
        label: Cacher la date avant le premier élément
        span: right
        tab: Teaser
        column: invisible
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_teaser_date:
        type: datepicker
        mode: date
        label: Date
        span: right
        column: invisible
        tab: Teaser
        required: true
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_date_position:
        label: Mode d'affichage du champs date
        type: dropdown
        span: left
        default: medium
        options:
            medium: Média-Texte Verticale
            large: Média-Texte Horizontale
        tab: Contenu
        column: invisible
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_teaser_image:
        label: Image
        type: mediafinder
        span: right
        mode: image
        column: invisible
        maxItems: 1
        tab: Teaser
        required: true
        translatable: false
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
        commentAbove: "La taille maximale idéale du fichier est de 1 Mo avec des dimensions de 520 x 520 pixels."
    field_teaser_image_alt:
        label: Description de l'image
        type: text
        commentAbove: La description de l'image. Utilisée à des fins de référencement (SEO).
        span: right
        column: invisible
        tab: Teaser
        required: true
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_teaser_video:
        label: Video
        type: text
        span: left
        column: invisible
        tab: Teaser
        trigger:
            action: show
            field: field_content_type
            condition: value[article]
    field_teaser_repeater:
        type: repeater
        label: Catégories
        prompt: Ajouter une catégorie
        tab: Teaser
        useTabs: false
        span: left
        showDuplicate: false
        form:
            fields:
                field_teaser_categories:
                    maxItems: 1
                    type: entries
                    source: Content\Taxonomy\Category
                    span: full
                    column: true
                    required: true
        trigger:
            action: show
            field: field_content_type
            condition: value[article]

