uuid: bb878a7b-4bf2-4830-ba58-7a158af1aad3
handle: Content\Block\Prestations
type: mixin
name: Prestations
fields:
    field_template:
        label: Maquette
        type: dropdown
        span: full
        default: column_white
        options:
            column_white: Widget avec colonnes et fond blanc
            column_cream_simple: Widget avec colonnes et fond crème
            row: Widget avec rangées
        tab: General
    field_title:
        type: mixin
        source: Content\Field\Richtext
    field_content:
        type: repeater
        label: Contenu
        maxItems: 5
        prompt: Ajouter
        tab: Éléments
        useTabs: true
        form:
            fields:
                field_subtitle:
                    label: Sous-titre
                    type: richeditor
                    size: tiny
                    toolbarButtons: paragraphFormat|inlineClass|bold|italic|underline|insertLineBreak|align|formatOL|formatUL|insertTable|insertPageLink|insertImage|insertHR|fullscreen|html
                    tab: Sous-titre
                field_description:
                    label: Description
                    type: richeditor
                    size: small
                    toolbarButtons: paragraphFormat|inlineClass|bold|italic|underline|insertLineBreak|align|formatOL|formatUL|insertTable|insertPageLink|insertImage|insertHR|fullscreen|html
                    tab: Description
                    trigger:
                        action: hide
                        field: ^field_template
                        condition: value[column_cream_simple]
                field_image:
                    label: Image
                    type: mediafinder
                    span: left
                    mode: image
                    maxItems: 1
                    tab: Image
                    thumbOptions: false
                    commentAbove: "La taille maximale idéale du fichier est de 1 Mo avec des dimensions de 95 x 65 pixels pour le widget avec colonnes et fond blanc, et de 80 x 72 pixels pour les deux autres widgets. Type de format idéal: SVG."
                field_image_alt:
                    label: Description de l'image
                    type: text
                    commentAbove: La description de l'image. Utilisée à des fins de référencement (SEO).
                    span: right
                    tab: Image
                field_link_items:
                    type: mixin
                    source: Content\LinkWithEstimationWithoutTrigger
                    tab: Link
                    trigger:
                        action: show
                        field: ^field_template
                        condition: value[row]

    field_button:
        type: mixin
        source: Content\Link
        tab: Link
