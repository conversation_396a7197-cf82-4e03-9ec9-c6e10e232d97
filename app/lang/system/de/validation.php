<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | such as the size rules. Feel free to tweak each of these messages.
    |
    */

    "accepted"         => 'Dieses Feld muss bestätigt werden.',
    "active_url"       => 'Dieses Feld ist keine gültige URL.',
    "after"            => '<PERSON>ses Feld muss ein Datum nach :date sein.',
    "alpha"            => 'Dieses Feld darf nur Buchstaben enthalten.',
    "alpha_dash"       => '<PERSON><PERSON>ld darf nur Buchstaben, Ziffern und Bindestriche enthalten.',
    "alpha_num"        => '<PERSON><PERSON>ld darf nur Buchstaben und Ziffern enthalten.',
    "array"            => 'Die<PERSON> Feld muss ein Array sein.',
    "before"           => 'Dieses Feld muss ein Datum vor :date sein.',
    "between"          => [
        "numeric" => 'Dieses Feld muss zwischen :min und :max liegen.',
        "file"    => 'Dieses Feld muss zwischen :min und :max kilobytes groß sein.',
        "string"  => 'Dieses Feld muss zwischen :min und :max Zeichen liegen.',
        "array"   => 'Dieses Feld muss zwischen :min und :max Elementen liegen.',
    ],
    "confirmed"        => 'Das Bestätigungfeld zu :attribute stimmt nicht überein.',
    "date"             => 'Dieses Feld ist kein gültiges Datum.',
    "date_format"      => 'Dieses Feld hat kein gültiges Datumsformat :format.',
    "different"        => 'Dieses Feld und :other müssen sich unterscheiden.',
    "digits"           => 'Dieses Feld benötigt :digits Zeichen.',
    "digits_between"   => 'Dieses Feld muss zwischen :min und :max Zeichenanzahl liegen.',
    "email"            => 'Das Format von Feld :attribute ist ungültig.',
    "exists"           => 'Das ausgewählte Feld ist ungültig.',
    "image"            => 'Dieses Feld muss ein Bild sein.',
    "in"               => 'Das ausgewählte Feld ist ungültig.',
    "integer"          => 'Dieses Feld muss eine Ganzzahl (integer) sein.',
    "ip"               => 'Dieses Feld muss eine gültige IP-Adresse sein.',
    "max"              => [
        "numeric" => 'Dieses Feld darf nicht größer als :max sein.',
        "file"    => 'Dieses Feld darf nicht größer als :max kilobytes sein.',
        "string"  => 'Dieses Feld darf nicht mehr als :max Zeichen haben.',
        "array"   => 'Dieses Feld darf nicht mehr als :max Elemente besitzen.',
    ],
    "mimes"            => 'Dieses Feld muss eine Datei des Typs: :values sein.',
    "extensions"       => 'Dieses Feld muss eine Erweiterung des Typs: :values sein.',
    "min"              => [
        "numeric" => 'Dieses Feld muss mindestens :min sein.',
        "file"    => 'Dieses Feld darf nicht kleiner als :min kilobytes sein.',
        "string"  => 'Dieses Feld darf nicht weniger als :min Zeichen haben.',
        "array"   => 'Dieses Feld darf nicht weniger als :min Elemente besitzen.',
    ],
    "not_in"           => 'Das ausgewählte Feld ist ungültig.',
    "numeric"          => 'Dieses Feld muss eine Zahl sein.',
    "regex"            => 'Das Format von Feld :attribute ist ungültig.',
    "required"         => 'Dieses Feld wird benötigt.',
    "required_if"      => 'Dieses Feld wird benötigt, wenn :other den Wert :value hat.',
    "required_with"    => 'Dieses Feld wird benötigt, wenn :values existiert.',
    "required_without" => 'Dieses Feld wird benötigt, wenn :values nicht existiert.',
    "same"             => 'Dieses Feld und :other müssen übereinstimmen.',
    "size"             => [
        "numeric" => 'Dieses Feld muss :size groß sein.',
        "file"    => 'Dieses Feld muss :size Kilobytes groß sein.',
        "string"  => 'Dieses Feld muss :size Zeichen beinhalten.',
        "array"   => 'Dieses Feld muss :size Elemente beinhalten.',
    ],
    "unique"           => 'Dieses Feld muss eindeutig sein. Der angegebene Wert wird schon benutzt.',
    "url"              => 'Dieses Feld ist keine gültige URL.',
    "phone"         => 'Geben Sie Ihre vollständige Nummer einschließlich der Vorwahl ein. Beispiel: +4179290xxxx',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'email' => [
            'unique' => "Es existiert bereits ein Konto mit diesen Angaben. Bitte geben Sie die E-Mail-Adresse ein, die mit der mitgeteilten Telefonnummer verknüpft ist."
        ],
        'phone_number' => [
            'unique' => "Es existiert bereits ein Konto mit diesen Angaben. Bitte geben Sie die Telefonnummer ein, die mit der mitgeteilten E-Mail-Adresse verknüpft ist."
        ],
        'floor' => [
            'lte' => "Es besteht bereits ein Konto mit diesen Kontaktdaten. Geben Sie bitte die Telefonnummer ein, welche zur eingegebenen E-Mail Adresse gehört. "
        ],
        'floor_nb' => [
            'gte' => "Das Stockwerk kann nicht grösser als die Gesamtzahl der Stockwerke des Gebäudes sein."
        ],
        'build_year' => [
            'before_or_equal' => "Die Anzahl Stockwerke des Gebäudes kann nicht kleiner sein als das Stockwerk der Wohnung."
        ],
        'renovation_year' => [
            'after_or_equal' => "Das Baujahr kann nicht nach dem Renovationsjahr liegen."
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [],

];
