<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | such as the size rules. Feel free to tweak each of these messages.
    |
    */

    "accepted"         => 'Le champ :attribute doit être accepté.',
    "active_url"       => 'Ce champ n’est pas une URL valide.',
    "after"            => 'Ce champ doit être une date après le :date.',
    "alpha"            => 'Ce champ ne peut contenir que des lettres.',
    "alpha_dash"       => 'Ce champ ne peut contenir que des lettres, des chiffres et des tirets.',
    "alpha_num"        => 'Ce champ ne peut contenir que des lettres et des chiffres.',
    "array"            => 'Ce champ doit être un groupe.',
    "before"           => 'Ce champ doit être une date avant le :date.',
    "between"          => [
        "numeric" => 'Ce champ doit être compris entre :min - :max.',
        "file"    => 'Ce champ doit être compris entre :min - :max kilooctets.',
        "string"  => 'Ce champ doit être compris entre :min - :max caractères.',
        "array"   => 'Ce champ doit être compris entre :min - :max objets.',
    ],
    "confirmed"        => 'Le champ de confirmation :attribute ne correspond pas.',
    "date"             => 'Ce champ n’est pas une date valide.',
    "date_format"      => 'Ce champ ne correspond pas au format :format.',
    "different"        => 'Ce champ et :other doivent être différents.',
    "digits"           => 'Ce champ doit être de :digits chiffres.',
    "digits_between"   => 'Ce champ doit être compris entre :min et :max chiffres.',
    "email"            => 'Le format du champ :attribute n’est pas valide.',
    "exists"           => 'Ce champ sélectionné n’est pas valide.',
    "image"            => 'Ce champ doit être une image.',
    "in"               => 'Ce champ sélectionné n’est pas valide.',
    "integer"          => 'Ce champ doit être un entier.',
    "ip"               => 'Ce champ doit être une adresse IP valide.',
    "max"              => [
        "numeric" => 'Ce champ ne peut pas être supérieure à :max.',
        "file"    => 'Ce champ ne peut pas être supérieure à :max kilooctets.',
        "string"  => 'Ce champ ne peut pas être supérieure à :max caractères.',
        "array"   => 'Ce champ ne peut pas être supérieure à :max objets.',
    ],
    "mimes"            => 'Ce champ doit être un fichier de type : :values.',
    "extensions"       => 'Ce champ doit être une extension de : :values.',
    "min"              => [
        "numeric" => 'Ce champ doit être au minimum de :min.',
        "file"    => 'Ce champ doit être au minimum de :min kilooctets.',
        "string"  => 'Ce champ doit être au minimum de :min caractères.',
        "array"   => 'Ce champ doit être au minimum de :min objets.',
    ],
    "not_in"           => 'Ce champ sélectionné n’est pas valide.',
    "numeric"          => 'Ce champ doit être un nombre.',
    "regex"            => 'Le format du champ :attribute n’est pas valide.',
    "required"         => 'Ce champ est obligatoire.',
    "required_if"      => 'Ce champ est obligatoire quand :other est :value.',
    "required_with"    => 'Ce champ est obligatoire quand :values est présent.',
    "required_without" => 'Ce champ est obligatoire quand :values est absent.',
    "same"             => 'Ce champ et :other doivent correspondre.',
    "size"             => [
        "numeric" => 'Ce champ doit être de :size.',
        "file"    => 'Ce champ doit être de :size kilooctets.',
        "string"  => 'Ce champ doit être de :size caractères.',
        "array"   => 'Ce champ doit contenir :size objets.',
    ],
    "unique"           => 'Ce champ doit être unique. La valeur renseignée est déjà utilisée.',
    "url"              => 'Ce champ n’est pas une URL valide.',
    "phone"         => 'Entrez votre numéro complet, y compris l\'indicatif. Exemple: +4179290xxxx',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'email' => [
            'unique' => "Un compte existe déjà avec ces coordonnées. Veuillez entrer l'adresse e-mail associée au numéro de téléphone communiqué."
        ],
        'phone_number' => [
            'unique' => "Un compte existe déjà avec ces coordonnées. Veuillez entrer le numéro de téléphone associé à l'adresse e-mail communiquée."
        ],
        'floor' => [
            'lte' => "L'étage ne peut pas être supérieur au nombre de niveaux de l'immeuble."
        ],
        'floor_nb' => [
            'gte' => "Le nombre de niveaux de l'immeuble ne peux pas être inférieur à l'étage de l'appartement."
        ],
        'build_year' => [
            'before_or_equal' => "L'année de construction ne peux pas être supérieure à l'année de rénovation."
        ],
        'renovation_year' => [
            'after_or_equal' => "L'année de rénovation ne peux pas être inférieure à l'année de construction."
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [],

];
