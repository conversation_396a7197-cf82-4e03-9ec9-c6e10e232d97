<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | such as the size rules. Feel free to tweak each of these messages.
    |
    */

    "accepted"         => 'Il campo :attribute deve essere accettato.',
    "active_url"       => 'Questo campo non è un URL valido.',
    "after"            => 'Questo campo deve essere una data successiva al :date.',
    "alpha"            => 'Questo campo accetta solo lettere.',
    "alpha_dash"       => 'Questo campo accetta solo lettere, numeri e trattini.',
    "alpha_num"        => 'Questo campo accetta solo lettere e numeri.',
    "array"            => 'Questo campo deve essere un gruppo.',
    "before"           => 'Questo campo deve essere una data precedente al :date.',
    "between"          => [
        "numeric" => 'Questo campo deve essere compreso tra :min e :max.',
        "file"    => 'Questo campo deve essere compreso tra :min e :max kilobytes.',
        "string"  => 'Questo campo deve essere compreso tra :min e :max caratteri.',
        "array"   => 'Questo campo deve essere compreso tra :min e :max oggetti.',
    ],
    "confirmed"        => 'Il campo di conferma :attribute non corrisponde.',
    "date"             => 'Questo campo non è una data valida.',
    "date_format"      => 'Questo campo non corresponde al formato :format.',
    "different"        => 'Questo campo e :other devono essere diversi.',
    "digits"           => 'Questo campo deve contenere :digits numeri.',
    "digits_between"   => 'Ce champ doit être compris entre :min et :max chiffres.',
    "email"            => 'Questo campo deve essere compreso tra :min e :max numeri.',
    "exists"           => 'Il campo selezionato non è valido.',
    "image"            => 'Questo campo deve essere un\'immagine.',
    "in"               => 'Il campo selezionato non è valido.',
    "integer"          => 'Questo campo deve essere un numero intero.',
    "ip"               => 'Questo campo deve essere un indirizzo IP valido.',
    "max"              => [
        "numeric" => 'Questo campo non può essere superiore di :max.',
        "file"    => 'Questo campo non può essere superiore di :max kilobytes.',
        "string"  => 'Questo campo non può essere superiore di :max caratteri.',
        "array"   => 'Questo campo non può essere superiore di :max oggetti.',
    ],
    "mimes"            => 'Questo campo deve essere un file di tipo: :values.',
    "extensions"       => 'Questo campo deve essere un\'estensione di: :values.',
    "min"              => [
        "numeric" => 'Questo campo deve essere di almeno :min.',
        "file"    => 'Questo campo deve essere di almeno :min kilobytes.',
        "string"  => 'Questo campo deve essere di almeno :min caratteri.',
        "array"   => 'Questo campo deve essere di almeno :min oggetti.',
    ],
    "not_in"           => 'Il campo selezionato non è valido.',
    "numeric"          => 'Questo campo deve essere un numero.',
    "regex"            => 'Il formato del campo :attribute non è valido.',
    "required"         => 'Questo campo è obbligatorio.',
    "required_if"      => 'Questo campo è obbligatorio se :other è :value.',
    "required_with"    => 'Questo campo è obbligatorio se :values è presente.',
    "required_without" => 'Questo campo è obbligatorio se :values è assente.',
    "same"             => 'Questo campo e :other devono corrispondere.',
    "size"             => [
        "numeric" => 'Questo campo deve essere di :size.',
        "file"    => 'Questo campo deve essere di :size kilobytes.',
        "string"  => 'Questo campo deve essere di :size caratteri.',
        "array"   => 'Questo campo deve essere di :size oggetti.',
    ],
    "unique"           => 'Questo campo deve essere unico. Il valore inserito è già utilizzato.',
    "url"              => 'Questo campo non è un URL valido.',
    "phone"         => 'Inserisci il tuo numero completo, compreso il prefisso. Esempio: +4179290xxxx',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'email' => [
            'unique' => "Questo conto è già esistente. La preghiamo di inserire l'indirizzo e-mail corrispondente al numero di telefono comunicato."
        ],
        'phone_number' => [
            'unique' => "Questo conto è già esistente. La preghiamo di inserire il numero di telefono corrispondente all'indirizzo e-mail comunicato."
        ],
        'floor' => [
            'lte' => "Il piano non può essere superiore al numero totale di piani dell'immobile."
        ],
        'floor_nb' => [
            'gte' => "Il numero totale di piani dell'immobile non può essere inferiore al piano dell'appartamento."
        ],
        'build_year' => [
            'before_or_equal' => "L'anno di costruzione non può essere superiore all'anno dei restauri."
        ],
        'renovation_year' => [
            'after_or_equal' => "L'anno dei restauri non può essere inferiore all'anno di costruzione."
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [],

];
