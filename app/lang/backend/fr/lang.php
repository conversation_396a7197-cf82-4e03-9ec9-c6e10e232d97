<?php return [
    'relation.create_name' => 'Créer',
    'fileupload' => [
        'clear' => 'Effacer',
        'delete_selected' => 'Supprimer la sélection',
        'upload' => 'Télécharger le fichier',
        'replace' => 'Remplace<PERSON> le fichier',
    ],
    'account' => [
        'login_prompt' => 'Bienvenue! Veuillez vous connecter à votre compte pour continuer.',
    ],
    'relation.delete' => 'Supprimer',
    'relation.delete_confirm' => 'Êtes vous sûr(e) ?',
    'import_export' => [
        'upload_csv_file' => '1. Envoyer un fichier CSV',
        'import_file' => 'Importer un fichier',
        'row' => 'Ligne :row',
        'first_row_contains_titles' => 'La première ligne contient les titres des colonnes',
        'first_row_contains_titles_desc' => 'Laissez coché si la première ligne du fichier CSV contient les titres des colonnes.',
        'match_columns' => '2. Faire correspondre les colonnes du fichier avec les champs du modèle de données',
        'file_columns' => 'Colonnes du fichier',
        'database_fields' => 'Champs de la base de données',
        'set_import_options' => '3. Fixer les options d’importation',
        'export_output_format' => '1. Format de sortie de l’export',
        'file_format' => 'Format du fichier',
        'standard_format' => 'Format Standard',
        'custom_format' => 'Format Personnalisé',
        'delimiter_char' => 'Caractère séparateur',
        'enclosure_char' => 'Caractère d’encadrement',
        'escape_char' => 'Caractère d’échappement',
        'select_columns' => '2. Choisissez les colonnes à exporter',
        'column' => 'Colonne',
        'columns' => 'Colonnes',
        'set_export_options' => '3. Définir les options d’exportation',
        'show_ignored_columns' => 'Voir les colonnes ignorées',
        'auto_match_columns' => 'Correspondance automatique des colonnes',
        'created' => 'Créés',
        'updated' => 'Mis à jour',
        'skipped' => 'Ignorés',
        'warnings' => 'Alertes',
        'errors' => 'Erreurs',
        'skipped_rows' => 'Lignes ignorées',
        'import_progress' => 'Progression de l’import',
        'processing' => 'Traitement',
        'import_error' => 'Erreur d’import',
        'upload_valid_csv' => 'Veuillez envoyer un fichier CSV valide.',
        'drop_column_here' => 'Déposez les colonnes ici...',
        'ignore_this_column' => 'Ignorer cette colonne',
        'processing_successful_line1' => 'Le processus d’export du fichier s’est terminé avec succès !',
        'processing_successful_line2' => 'Le navigateur devrait automatiquement vous rediriger vers le téléchargement du fichier.',
        'export_progress' => 'Progression de l’export',
        'export_error' => 'Erreur d’export',
        'column_preview' => 'Prévisualisation des colonnes',
        'file_not_found_error' => 'Fichier non trouvé',
        'empty_error' => 'Il n‘y a aucune donnée à exporter',
        'empty_import_columns_error' => 'Veuillez indiquer quelques colonnes à importer.',
        'match_some_column_error' => 'Veuillez d’abord faire correspondre quelques colonnes.',
        'required_match_column_error' => 'Veuillez faire correspondre la colonne obligatoire :label.',
        'empty_export_columns_error' => 'Veuillez indiquer quelques colonnes à exporter.',
        'behavior_missing_uselist_error' => 'Vous devez implémenter le behavior ListController avec l’option d’export "useList" activée.',
        'missing_model_class_error' => 'Veuillez préciser la propriété modelClass pour :type',
        'missing_column_id_error' => 'Identifiant de colonne manquant',
        'unknown_column_error' => 'Colonne inconnue',
        'encoding_not_supported_error' => 'L’encodage de votre fichier source n’est pas reconnu. Veuillez sélectionner le format d’import personnalisé avec l’encodage adapté pour importer votre fichier.',
        'encoding_format' => 'Encodage du fichier',
        'encodings' => [
            'utf_8' => 'UTF-8',
            'us_ascii' => 'US-ASCII',
            'iso_8859_1' => 'ISO-8859-1 (Latin-1, européen occidental)',
            'iso_8859_2' => 'ISO-8859-2 (Latin-2, européen central)',
            'iso_8859_3' => 'ISO-8859-3 (Latin-3, européen du Sud)',
            'iso_8859_4' => 'ISO-8859-4 (Latin-4, européen du Nord)',
            'iso_8859_5' => 'ISO-8859-5 (Latin, cyrillique)',
            'iso_8859_6' => 'ISO-8859-6 (Latin, arabe)',
            'iso_8859_7' => 'ISO-8859-7 (Latin, grec)',
            'iso_8859_8' => 'ISO-8859-8 (Latin, hébreu)',
            'iso_8859_0' => 'ISO-8859-9 (Latin-5, turc)',
            'iso_8859_10' => 'ISO-8859-10 (Latin-6, nordique)',
            'iso_8859_11' => 'ISO-8859-11 (Latin, thaï)',
            'iso_8859_13' => 'ISO-8859-13 (Latin-7, balte)',
            'iso_8859_14' => 'ISO-8859-14 (Latin-8, celtique)',
            'iso_8859_15' => 'ISO-8859-15 (Latin-9, européen occidental révisé avec le signe euro)',
            'windows_1251' => 'Windows-1251 (CP1251)',
            'windows_1252' => 'Windows-1252 (CP1252)'
        ]
    ]
];
