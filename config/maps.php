<?php

/**
 * Google Maps configuration
 */
return [
    // client side key, to display the Google Maps map
    'key' => 'AIzaSyBkWD_pliWS9QKVTTuQFu385TPVtUdJjuM',
    // server side key, used to get distance between the properties
    'server_key' => 'AIzaSyAaCKHF9v0D7QvH_IxWU2DroVhq7D7LBpc',
    // google maps endpoint to get the distance between 2 addresses
    'distance_matrix_endpoint' => 'https://maps.googleapis.com/maps/api/distancematrix/json?' .
    'region=CH',
    'geocode_endpoint' => 'https://maps.googleapis.com/maps/api/geocode/json'
];
