<?php
return [
    /*
    |--------------------------------------------------------------------------
    | Publisher Publicators configuration
    |--------------------------------------------------------------------------
    |
    | List the publicators and adaptor configuration, credentials and other information about Publisher
    | plugins.
    |
    | Use the GrahamCampbell flysystem integration to Laravel
    | See documentation :
    |   +   https://flysystem.thephpleague.com/
    |   +   https://github.com/GrahamCampbell/Laravel-Flysystem
    |
    */
    'publicators' => [
        'homegate' => [
            // Enable publishing or not (false by default, avoid errors)
            'enabled' => true,
            // Connector
            'driver' => 'ftp',
            // preprod
            // prod
            'host' => 'ftp.homegate.ch',
            'username' => 'd587',
            'password' => 'hom587gat',
// was working at first, but the images were corrupted since the 20.11.2018 so we went back to binary mode
//            'transferMode' => FTP_ASCII,
            // IDX fields
            'fields' => plugins_path('kiiz/publisher/classes/publicators/homegate/config.yaml'),
            // FileManipulator and Transporter details
            'manipulator' => 'csv',
            'streamFilters' => 'convert.iconv.UTF-8/ISO-8859-1//TRANSLIT', // Use '|' to separate the filters
            'newline' => "\r\n",
            'delimiter' => "#",
            'path' => storage_path('temp/unload.txt'), // Manipulator & Transporter
            // Transformer class for publicator
            'transformer' => \Kiiz\Publisher\Classes\Publicators\Homegate\Transformer::class,
            // Transporter
            'destination' => 'data/unload.txt',
            'directories' => [
                'images',
                'movies',
                'data',
                'doc'
            ]
        ],
        'immoscout' => [
            'enabled' => false,
            'driver' => 'ftp',
            'host' => 'ftp.immoscout24.ch',
            'username' => 'xkc4reeb',
            'password' => 'ntm7mcty5e',
            'fields' => plugins_path('kiiz/publisher/classes/publicators/immoscout/config.yaml'),
            'manipulator' => 'csv',
            'streamFilters' => 'convert.iconv.UTF-8/ISO-8859-1//TRANSLIT',
            'newline' => "\r\n",
            'delimiter' => "#",
            'path' => storage_path('temp/unload.txt'),
            'transformer' => \Kiiz\Publisher\Classes\Publicators\Immoscout\Transformer::class,
            'destination' => 'unload.txt',
            'directories' => [
                '/'
            ]
        ]
    ]
];
