<?php
return [
    /*
    |--------------------------------------------------------------------------
    | Estimation API configuration
    |--------------------------------------------------------------------------
    |
    | List the endpoints, credentials and other information about Estimation
    | plugins.
    |
    */
    'plugins' => [
        'cifi' => [
            'enabled' => false,
            // login endpoint to retrieve authorization token
            'authEndpoint' => 'https://services.iazi.ch/api/auth/v2',
            'estimationEndpoint' => 'https://servicesr.iazi.ch/api/models/v1',
            'microEndpoint' => 'https://services.iazi.ch/api/micro/v1/ratings',
            'addressEndpoint' => 'https://services.iazi.ch/api/address/v1/geocode',
            // prod
            'user' => '<EMAIL>',
            'password' => 'Yg(7nfuXo?',
            'app' => 'address,micro,modelr',
            'locale' => 'fr-CH'
        ],
        'pricehubble' => [
            'enabled' => true,
            // login endpoint to retrieve authorization token
            'authEndpoint' => 'https://api.pricehubble.com/auth/login/credentials',
            'estimationEndpoint' => 'https://api.pricehubble.com/api/v1',
            'user' => 'project_nutella',
            'password' => '8&NQ6B$nUZe;yV6P7?Bg',
            'markup' => 0.076
        ],
        'fahrlander_soap' => [
            'enabled' => true,
            'endpoint' => 'https://webservice.fpre.ch/2.1/fpre_webservice.asmx?WSDL',
            'mikrohEndpoint' => 'https://webservice.fpre.ch/mikroh/fpre_mikroh.asmx?WSDL',
            'user' => 'comina',
            'password' => 'r3toVNpydk',
            // requires local cert to connect to remote server
            'cert_file' => 'plugins/kiiz/estimator/classes/estimator/fahrlander/fahrlander.pem',
            'cert_passphrase' => 'KEJhuQv90n6UarO7Wr7K',
            'namespace' => 'webservice.fpre.ch',
            'markup' => 0.093
        ],
        'fahrlander' => [
            'enabled' => true,
            'markup' => 0.093,
            'client_id' => 'b218f321-7d37-4ff0-9212-cff4a2ca72d3',
            'client_secret' => '****************************************'
        ],
        'srei' => [
            'enabled' => false,
            'endpoint' => 'https://api.meta-sys.ch/v1/hedo',
            'appKey' => 'FgMFSz85x294eg3V',
            // waiting time between the creation of the estimation and the reading
            'throttle' => 35
        ],
        'wuestpartner' => [
            'enabled' => true,
            'endpoint' => 'https://ws.wuestdimensions.com/ws',
            'token' => 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZWNoX2NvbWluYSIsImV4cCI6MTYyMjQxMjAwMCwianRpIjoiNTUzIn0.VJc8pCuC7X0OF4PbHug31brIvpHzpE2IJqg8FoY63uAqDlhIKNpN-2o8Q6P95UG233ZfFlCiQ9lrbFVBxUkQSQ',
            'version' => 'dimensions.v2.23.2'
        ],
    ],
    'active_plugins' => explode(',' ,env('ACTIVE_PLUGINS', 'fahrlander,pricehubble,wuestpartner'))
];
