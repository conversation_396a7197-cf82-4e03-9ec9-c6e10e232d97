<?php

return [
    // excluded events for the front end calendar
    'excludedEventsIds' => [1010],
    // when creating a new event, it will automatically split the slot into slices of 90 minutes each
    'slotSplitDuration' => '+60 minutes',
    // when the owner inputs his/her visit's availability, we display availability from now until the below offset
    'visitAvailabilityOffset' => '+1 days',
    // offset to warn the owner that there's no more visits planned in the near future (MySQL interval)
    'visitAvailabilityOffsetReminder' => '7 day',
    // for Solo contracts (user handles the visits), the availability is up to him, we don't need to
    // organize the calendar so we put only 1 day of offset
    'visitAvailabilityOffsetSolo' => '+1 days',
    // number of days in advance that we show to the owner to input his/her availability
    'visitAvailabilityDuration' => '21',
    // bonus added to the itinerary's duration between 2 events.
    'itineraryBonus' => 1.2, // +30%
    // hides the non confirmed events that take place in the next 48h from the current date (MySQL date interval)
    'shortTermVisitInterval' => '2 DAY',
    // the minimum time in seconds that a backend user can stay in HQ before going to another appointment by car
    'distanceDelayBonus' => 1800
];
