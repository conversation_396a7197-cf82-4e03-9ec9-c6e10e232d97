<?php

return [
    // each how many days do we notifiy the BO users to make a property report
    'reportDaysInterval' => 7,
    // the backend user's without an admin account only see the properties within a certain range of time
    // (MYSQL interval)
    'duration_restriction' => '1 WEEK',
    // max photos of a property that are published into portals
    'galleryLimit' => 12,
    // gallery limit for PDF
    'pdfGalleryLimit' => 10,
    // the homegate.ch views will be multiplied by this factor which emulates the other portal's views
    'statisticViewsPonderation' => 1.********,
    // add here the properties that should not show in the front end carousel,
    // cache must be cleared!!!
    'excludedFromCarousel' => [15],
    // when sending the weekly stats report to the owner, the property has to be published since X days
    'autoReportMinPublicationDays' => 10,
    // statistics range that is shown on the property's report
    'globalStatsMaxWeek' => 30,
    // auto generated task list when a property is created
    'initTasks' => [
        [
            'label' => 'kiiz.property::lang.task.is-publication-sent-to-client',
            'interval' => '+1 days',
            'with_notification' => false,
            'assigned_user' => \Kiiz\Property\Classes\TaskInitializer::TYPE_COLLABORATOR
        ],
        // to create a task 2 days after creation of property for the photograph to contact owner if no appointment set
        [
            'label' => 'kiiz.property::lang.task.create-photograph-task-contact-owner',
            'interval' => '+48 hours',
            'with_notification' => FALSE,
            'job_handler' => \Kiiz\Calendar\Jobs\PhotographContactOwner::class,
            'assigned_user' => \Kiiz\Property\Classes\TaskInitializer::TYPE_NONE,
            'opening_hours_id' => 'robot',
            'unique_id' => 'create-photograph-task-contact-owner',
        ],
    ],
    // automatic weekly tasks that we generate each monday to do the property follow up
    'weeklyTasks' => [
        [
            'label' => 'kiiz.property::lang.task.handle-availability',
            'with_notification' => false,
            // a check is performed in the below field to verify if the task is eligible for creation
            'check_eligibility' => function (\Kiiz\Property\Models\Property $property) {
                return !$property->owner_handles_visit_availability;
            }
        ]
    ],
    // contacts coming from publications portals
    'interestedContact' => [
        'homegate' => [
            'fromAddress' => json_decode(env('PROPERTY_INTERESTED_CONTACT_HOMEGATE_FROM', '[]'), true)
        ],
        'immoscout' => [
            'fromAddress' => json_decode(env('PROPERTY_INTERESTED_CONTACT_IMMOSCOUT_FROM', '[]'), true),
        ]
    ],
    // add here the property short links
    'shortLinks' => [
        [
            'slug' => 'morgins',
            'property_slug' => '20181221-1ER'
        ],
        [
            'slug' => 'grimisuat',
            'property_slug' => '20181206-P5F'
        ],
        [
            'slug' => 'bramois',
            'property_slug' => '20181206-ZOP'
        ],
        [
            'slug' => 'leukerbad',
            'property_slug' => '20181206-GZF'
        ]
    ]
];
