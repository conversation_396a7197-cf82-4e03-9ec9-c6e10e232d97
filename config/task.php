<?php
/**
 * Automated tasks configuration
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018 Kiiz SA
 */

use Kiiz\Backend\Classes\GlobalConst;

return [
    /**
     * Estimator
     */
    // automatic follow up for valuation only (+2h after registration)
    /*'estimator:valuation-followup-1' => [
        'label' => 'Follow up email (Valuation Only) #1',
        'interval' => '+2 hours',
        'preset' => 'estimator:valuation-followup-1',
        'backend_user_id' => GlobalConst::BACKEND_USER_ID_MARC,
        'type' => 'followup',
        'group_id' => \Kiiz\User\Models\UserGroup::GROUP_VALUATION_ID
    ],
    // automatic follow up for full service (+2h after registration)
    'estimator:full-service-followup-1' => [
        'label' => 'Follow up email (Full Service) #1',
        'interval' => '+2 hours',
        'preset' => 'estimator:full-service-followup-1',
        'backend_user_id' => GlobalConst::BACKEND_USER_ID_MARC,
        'type' => 'followup',
        'group_id' => \Kiiz\User\Models\UserGroup::GROUP_FULL_ID,
    ],
    // automatic follow up for all (valuation and full service) (+48h after registration)
    'estimator:all-followup-2' => [
        'label' => 'Follow up email #2',
        'interval' => '+2 days', // after 1st followup
        'preset' => 'estimator:all-followup-2',
        'backend_user_id' => GlobalConst::BACKEND_USER_ID_MARC,
        'type' => 'followup',
        'reply_last_conversation' => true
    ],
    // automatic follow up for all (valuation and full service) (+5d after registration)
    'estimator:all-followup-3' => [
        'label' => 'Follow up email #3',
        'interval' => '+3 days', // after 2nd followup
        'preset' => 'estimator:all-followup-3',
        'backend_user_id' => GlobalConst::BACKEND_USER_ID_MARC,
        'type' => 'followup',
        'reply_last_conversation' => true
    ],*/
    'estimator:ceo-message' => [
        'label' => 'kiiz.estimator::lang.task.ceo-message',
        'interval' => '+1 days', // after 2nd followup
        'preset' => 'estimator:ceo-message',
        'type' => 'followup',
        'reply_last_conversation' => false,
        'create_on_register' => true,
        // this message won't be cleared if the customer send us an e-mail
        'cancel_on_inbound_message' => false,
        'group_id' => \Kiiz\User\Models\UserGroup::GROUP_VALUATION_ID
    ],
    // call to dun the client
    'estimator:dun-client-phone' => [
        'label' => 'kiiz.estimator::lang.task.dun-client-phone',
        'interval' => '+48 hours',
        'type' => 'followup',
        'create_on_register' => false
    ],
    // automatic contract
    'estimator:send-contract' => [
        'label' => 'Send contract email',
        'preset' => 'estimator:send-contract',
    ],

    /**
     * Calendar
     */
    // visit reminder email to the owner
    'calendar:mandate-owner-reminder' => [
        'label' => 'kiiz.calendar::lang.task.mandate_owner_reminder',
        'interval' => '-24 hours', // before visit time
    ],
    // mandate reminder sms to the backend user
    'calendar:mandate-backend-user-sms-reminder' => [
        'label' => 'kiiz.calendar::lang.task.mandate_backend_user_sms_reminder',
        'interval' => '-24 hours', // before visit time,
    ],
    // visit reminder email to the owner
    'calendar:visit-owner-reminder' => [
        'label' => 'kiiz.calendar::lang.task.visit_owner_reminder',
        'interval' => '-24 hours', // before visit time
    ],
    // visit reminder sms to the visitor
    'calendar:visit-visitor-sms-reminder' => [
        'label' => 'kiiz.calendar::lang.task.visit_sms_reminder',
        'preset' => 'calendar:visit-visitor-sms-reminder',
        'minInterval' => 7200, // 2h before visit
    ],
    // visit reminder sms to the owner
    'calendar:visit-owner-sms-reminder' => [
        'label' => 'kiiz.calendar::lang.task.visit_sms_reminder',
        'preset' => 'calendar:visit-owner-sms-reminder',
        'minInterval' => 7200, // 2h before visit
    ],
    // visit reminder sms to the backend user
    'calendar:visit-backend-user-sms-reminder' => [
        'label' => 'kiiz.calendar::lang.task.visit_backend_user_sms_reminder',
        'interval' => '-24 hours', // before visit time,
    ],
    // visit followup to the visitor
    'calendar:visit-visitor-followup' => [
        'label' => 'kiiz.calendar::lang.task.visit_visitor_followup',
        'preset' => 'calendar:visit-visitor-followup',
        'interval' => '+1 hours', // 1h after visit
        // fallback user if there's no host
    ],
    // visit followup to the visitor if he has not replied to the first follow up
    'calendar:visit-visitor-followup-2' => [
        'label' => 'kiiz.calendar::lang.task.visit_visitor_followup_2',
        'preset' => 'calendar:visit-visitor-followup-2',
        'interval' => '+4 days',
    ],
    // photo reminder sms to the backend user
    'calendar:photo-backend-user-sms-reminder' => [
        'label' => 'kiiz.calendar::lang.task.photo_backend_user_sms_reminder',
        'preset' => 'calendar:photo-backend-user-sms-reminder',
        'interval' => '-24 hours', // before visit time
    ],
    // notifies the host of the property that there's a problem with the owner's availability
    'calendar:visit-availability-to-owner-danger' => [
        'label' => 'kiiz.calendar::lang.task.visit-availability-to-owner-danger',
        'interval' => '0 days',
        'with_notification' => true
    ],
    // the delay to automatically reject estimates if the client doesen't fill the e-mail form
    'estimator:auto-reject-estimate' => [
        'interval' => '+2 hours'
    ],

    /**
     * Property
     */
    // task for photograph to set his availability
    'property:photograph-set-availability' => [
        'label' => 'kiiz.property::lang.task.photograph-set-availability',
        'interval' => '+0 minutes',
        'with_notification' => TRUE,
    ],
    // task to photograph to contact owner
    'property:photograph-contact-owner' => [
        'label' => 'kiiz.property::lang.task.photograph-contact-owner',
        'interval' => '+0 minutes',
        'with_notification' => TRUE,
    ],
    // when the property enters the 'writing-content' status
    'property:write-content' => [
        'label' => 'kiiz.property::lang.task.write-publication-description',
        'with_notification' => true
    ],
    'property:is-publication-validated-by-client' => [
        'label' => 'kiiz.property::lang.task.is-publication-validated-by-client',
        'with_notification' => false,
        'interval' => '+3 days'
    ],
    // visit calendar email to interested visitor. The sender is the account of the property
    'property:visitor-calendar' => [
        'label' => 'kiiz.calendar::lang.task.visitor_calendar',
        'interval' => '+2 minutes',
        'preset' => 'property:visitor-calendar'
    ],
    // already sold email to interested visitor. The sender is the account of the property
    'property:visitor-sold' => [
        'label' => 'kiiz.calendar::lang.task.visitor_sold',
        'interval' => '+2 minutes',
        'preset' => 'property:visitor-sold'
    ],
    // share the publication links with the customer when the property is published
    'property:share-publication-links' => [
        'label' => 'kiiz.property::lang.task.share-publication-links',
        'interval' => '+1 days',
        'with_notification' => false,
    ],
    // mailing list to potential buyers
    'property:mailing-list-potential-buyers' => [
        'label' => 'kiiz.property::lang.task.mailing-list-potential-buyers',
        'interval' => '+1 hour',
        'with_notification' => false,
        'preset' => 'property:mailing-list-potential-buyers'
    ],
    // mailing list to potential buyers and interested person for a lower price
    'property:mailing-lower-price' => [
        'label' => 'kiiz.property::lang.task.mailing-lower-price',
        'interval' => '+30 minutes',
        'with_notification' => false,
        'preset' => 'property:mailing-lower-price'
    ],
    // review call with the owner after two weeks
    'task.review-call-1-1' => [
        'label' => 'kiiz.property::lang.task.review-call-1',
        'interval' => '+14 days',
        'type' => 'review-call'
    ],
    // second review call with the owner
    'property:review-call-1' => [
        'label' => 'kiiz.property::lang.task.review-call-1',
        'interval' => '+28 days',
        'type' => 'review-call'
    ],
    // third review call with the owner
    'property:review-call-2' => [
        'label' => 'kiiz.property::lang.task.review-call-2',
        'interval' => '+56 days',
        'type' => 'review-call'
    ],
    // fourth review call with the owner
    'property:review-call-3' => [
        'label' => 'kiiz.property::lang.task.review-call-3',
        'interval' => '+70 days',
        'type' => 'review-call'
    ],
    // sent when the visitor sends an offer for a property
    'property:visitor-offer-confirm' => [
        'label' => 'kiiz.property::lang.task.visitor_offer_confirm',
        'preset' => 'property:visitor-offer-confirm'
    ],
    // first review call with the owner
    'property:send-bill' => [
        'label' => 'kiiz.property::lang.task.send-bill',
        'interval' => '+1 days',
        'with_notification' => FALSE,
        'preset' => 'property:mailing-facture'
    ],
    // review visitor not interested feedback
    'property:review-visitor-not-interested-feedback' => [
        'label' => 'kiiz.theme.property.notinterested.task.label',
    ],
    // review visitor interested feedback
    'property-offer:review-offer' => [
        'label' => 'kiiz.property::lang.task.review_offer',
    ],
    'property-offer:collect-notary-documents' => [
        'label' => 'kiiz.property::lang.task.collect_notary_documents',
    ],
    'property-offer:negotiate-notify-buyer' => [
        'label' => 'kiiz.property::lang.task.negotiate_notify_buyer',
    ],
    'property-offer:rejected-notify-buyer' => [
        'label' => 'kiiz.property::lang.task.rejected_notify_buyer',
    ],
    // review notary added by the client
    'property:verify-notary-contact' => [
        'label' => 'kiiz.property::lang.task.verify-notary-contact',
        'with_notification' => true
    ],
    // closing email to survey the owner
    'property:notify-owner-satisfaction' => [
        'label' => 'kiiz.property::lang.task.owner_satisfaction',
        'preset' => 'property:owner-satisfaction',
        'interval' => '+1 days'
    ],
    // SMS sent to the owner when the notary appointment takes place
    'property:notify-owner-notary-appointment' => [
        'label' => 'kiiz.property::lang.task.owner_notary_appointment',
        'preset' => 'property:owner-notary-appointment',
    ],
    // SMS sent to the owner when the notary appointment takes place
    'property:notify-buyer-notary-appointment' => [
        'label' => 'kiiz.property::lang.task.buyer_notary_appointment',
        'preset' => 'property:buyer-notary-appointment',
    ],
    'property:mandate-expiration-warning' => [
        'label' => 'kiiz.property::lang.mandate_expiration_warning_title',
        'with_notification' => true
    ],
    'property:mandate-expired' => [
        'label' => 'kiiz.property::lang.mandate_expired_title',
        'with_notification' => true
    ]
];
