<?php

return [
    'enabled' => env('FRONTAPP_ENABLE', false),
    'verify' => false,
    'endpoint' => 'https://api2.frontapp.com',
    'token' => env('FRONTAPP_TOKEN', 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzY29wZXMiOlsic2hhcmVkOioiLCJwcml2YXRlOioiXSwiaWF0IjoxNTQxMDg1MTI3LCJpc3MiOiJmcm9udCIsInN1YiI6ImtpaXpfc2EiLCJqdGkiOiI0ZDBmYTVjMjRiYmJjZDZhIn0.gHCtcr3xQiTqOJKSLIzWNGz2iwXizAJsg-NzvxNN6yI'),
    'secret' =>  env('FRONTAPP_SECRET','f2fd60060752f999e52f1a0829ca121b'),
    // add here all the transactional email that must be sent via the Front App API, the will automatically be injected
    // in the conversations of the client
    'apiViews' => [
        'kiiz.mailer::mail.default' => [],
        'kiiz.mailer::mail.punch' => [],
        'kiiz.mailer::mail.empty' => [],
        'kiiz.estimator::mail.created' => [],
        'kiiz.user::mail.welcome' => [],
        'kiiz.user::mail.welcome-v2' => [],
        'kiiz.user::mail.ceo-message' => [],
        'kiiz.calendar::mail.booked' => [],
        'kiiz.task::mail.assigned' => [],
        'kiiz.task::mail.digest' => [],
        'kiiz.calendar::mail.photo-cancel-owner' => [],
        'kiiz.calendar::mail.photo-confirm-owner' => [],
        'kiiz.calendar::mail.photo-move-owner' => [],
        'kiiz.calendar::mail.single-event-backend-notification' => [],
        'kiiz.calendar::mail.visit-cancel-owner' => [],
        'kiiz.calendar::mail.visit-cancel-visitor' => [],
        'kiiz.calendar::mail.visit-confirm-owner' => [],
        'kiiz.calendar::mail.visit-confirm-visitor' => [],
        'kiiz.calendar::mail.visit-move-owner' => [],
        'kiiz.calendar::mail.visit-move-visitor' => [],
        'kiiz.calendar::mail.visit-reminder-owner' => [],
        'kiiz.calendar::mail.mandate-confirm-owner' => [],
        'kiiz.calendar::mail.mandate-confirm-backenduser' => [],
        'kiiz.calendar::mail.mandate-cancel-owner' => [],
        'kiiz.calendar::mail.mandate-cancel-backenduser' => [],
        'kiiz.calendar::mail.mandate-move-owner' => [],
        'kiiz.calendar::mail.mandate-move-backenduser' => [],
        'kiiz.calendar::mail.mandate-reminder-owner' => [],
        'kiiz.property::mail.owner-weekly-report' => [],
        'kiiz.property::mail.reminder-pending-interested-person' => [],
        'kiiz.mailer::mail.backend-notification' => [],
        'kiiz.property::mail.client-feedback-backenduser' => [],
    ],
    // add here the views that you wish to store as a conversation but that won't be handled by FrontApp
    'bypassedViews' => [],
    // id of the mailbox (email)
    'channelId' =>  env('FRONTAPP_CHANNEL_ID','cha_hnka'),
    // preprod doesen't have an SMS number
    'smsChannelId' => env('FRONTAPP_SMS_CHANNEL_ID'),
    // used when there's not sms channel configured. You can put here an e-mail address that will receive all the SMS
    // if you add the %s token, it will be replaced with the phone number
    'smsFallbackEmail' => env('FRONTAPP_SMS_FALLBACK_EMAIL'),
    // all undelivered SMS (not mobile phone) will be delivered to:
    'undeliveredSms' => env('FRONTAPP_UNDELIVERED_SMS_USER_ID'),
];
