<?php
/**
 * @see http://symfony.com/doc/current/workflow/usage.html
 */

$propertyStatus = [
    'draft',
    'awaiting-photo-appointment',
    'photo-appointment-confirmed',
    'awaiting-photo-upload',
    'writing-content',
    'validation',
    'published',
    'offer',
    'sale-in-progress',
    'notary',
    'sold',
    'archived',
    'canceled'
];
return [
    /**
     * Property Management Workflow
     */
    'property' => [
        'type' => 'state_machine',
        'marking_store' => [
            'type' => 'single_state',
            'property' => 'status', // database column that stores the state
        ],
        'supports' => [\Kiiz\Property\Models\Property::class],
        'places' => $propertyStatus,
        'transitions' => [
            'draft' => [
                'from' => ['awaiting-photo-appointment'],
                'to' => 'draft',
            ],
            'await-photo-appointment' => [
                'from' => ['draft', 'photo-appointment-confirmed'],
                'to' => 'awaiting-photo-appointment',
            ],
            'confirm-photo-appointment' => [
                'from' => ['awaiting-photo-appointment', 'awaiting-photo-upload'],
                'to' => 'photo-appointment-confirmed',
            ],
            'await-photo-upload' => [
                'from' => ['photo-appointment-confirmed', 'writing-content'],
                'to' => 'awaiting-photo-upload',
            ],
            'write-content' => [
                'from' => ['photo-appointment-confirmed', 'awaiting-photo-upload', 'validation'],
                'to' => 'writing-content',
            ],
            'wait-validation' => [
                'from' => ['writing-content'],
                'to' => 'validation',
            ],
            'publish' => [
                'from' => ['validation', 'offer'],
                'to' => 'published',
            ],
            'unpublish' => [
                'from' => ['published', 'sold', 'offer', 'sale-in-progress', 'notary'],
                'to' => 'validation',
            ],
            'receive-offer' => [
                'from' => ['published', 'sale-in-progress'],
                'to' => 'offer',
            ],
            'sale-in-progress' => [
                'from' => ['offer', 'notary'],
                'to' => 'sale-in-progress',
            ],
            'go-to-notary' => [
                'from' => ['sale-in-progress', 'sold'],
                'to' => 'notary',
                'formFields' => 'kiiz/property/models/property/go-to-notary.yaml',
            ],
            'sell' => [
                'from' => ['notary'],
                'to' => 'sold',
                'formFields' => 'kiiz/property/models/property/set-transaction-price.yaml',
            ],
            'archive' => [
                'from' => ['sold', 'canceled'],
                'to' => 'archived',
            ],
            'cancel' => [
                'from' => $propertyStatus,
                'to' => 'canceled',
            ],
            're-open' => [
                'from' => ['canceled', 'archived'],
                'to' => 'draft'
            ]
        ]
    ],
    /**
     * Workflow of people that are interested in a property
     */
    'property_interested' => [
        'type' => 'state_machine',
        'marking_store' => [
            'type' => 'single_state',
            'property' => 'status', // database column that stores the state
        ],
        'supports' => [\Kiiz\Property\Models\PropertyInterested::class],
        'places' => [
            'pending',
            'visit_planned',
            'visited',
            'offer',
            'offer_accepted',
            'closed',
        ],
        'transitions' => [
            'plan_visit' => [
                'from' => ['pending'],
                'to' => 'visit_planned'
            ],
            'visit' => [
                'from' => ['visit_planned'],
                'to' => 'visited'
            ],
            'make_offer' => [
                'from' => ['visit_planned', 'visited'],
                'to' => 'offer'
            ],
            'accept_offer' => [
                'from' => ['offer'],
                'to' => 'offer_accepted'
            ],
            'close' => [
                'from' => [
                    'pending',
                    'visit_planned',
                    'visited',
                    'offer',
                    'offer_accepted'
                ],
                'to' => 'closed'
            ],
        ]
    ],

    /**
     * Workflow of people that are interested in a property
     */
    'property_offer' => [
        'type' => 'state_machine',
        'marking_store' => [
            'type' => 'single_state',
            'property' => 'status', // database column that stores the state
        ],
        'supports' => [\Kiiz\Property\Models\PropertyOffer::class],
        'places' => [
            'new',
            'negotiating',
            'accepted',
            'rejected'
        ],
        'transitions' => [
            'new' => [
                'from' => [],
                'to' => 'new',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-new.yaml',
                'skipRedirect' => true
            ],
            'negotiate' => [
                'from' => ['new', 'accepted'],
                'to' => 'negotiating',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-negotiate.yaml',
                'skipRedirect' => true
            ],
            're_negotiate' => [
                'from' => ['negotiating'],
                'to' => 'negotiating',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-negotiate.yaml',
                'skipRedirect' => true
            ],
            'accept' => [
                'from' => ['new', 'negotiating'],
                'to' => 'accepted',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-accept.yaml',
                'skipRedirect' => true
            ],
            'reject' => [
                'from' => ['new', 'negotiating', 'accepted'],
                'to' => 'rejected',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-reject.yaml',
                'skipRedirect' => true
            ],
            're_open' => [
                'from' => ['rejected'],
                'to' => 'new',
                'formFields' => 'kiiz/property/models/propertyoffer/transition-re-open.yaml',
                'skipRedirect' => true
            ]
        ]
    ],

    /**
     * Estimator Workflow
     */
    'estimator' => [
        'type' => 'state_machine',
        'marking_store' => [
            'type' => 'single_state',
            'property' => 'status', // database column that stores the state
        ],
        'supports' => [\Kiiz\Estimator\Models\Estimate::class],
        'places' => [
            'guide-downloaded',
            'awaiting-client',
            'pending',
            'progress',
            'to-dun',
            'estimated',
            'contract-sent',
            'validated',
            'rejected',
            'error',
            'valuation-sold'
        ],
        'transitions' => [
            'open' => [
                'from' => ['awaiting-client', 'rejected', 'guide-downloaded'],
                'to' => 'pending'
            ],
            'dun' => [
                'from' => ['awaiting-client', 'pending'],
                'to' => 'to-dun'
            ],
            'estimate' => [
                'from' => ['pending', 'error', 'to-dun'],
                'to' => 'estimated',
                // base YAML form field configuration
                // the below class will dynamically extend the form with 2 methods, prepareFields(adds dynamic fields)
                //and prepareDefaultValues (fills the form with default values)
                'formDynamizer' => \Kiiz\Estimator\Classes\Workflow\EstimateFormDynamizer::class
            ],
            're-estimate' => [
                'from' => ['validated'],
                'to' => 'validated',
                'formDynamizer' => \Kiiz\Estimator\Classes\Workflow\EstimateFormDynamizer::class
            ],
            're-estimate-estimated' => [
                'from' => ['estimated'],
                'to' => 'estimated',
                'formDynamizer' => \Kiiz\Estimator\Classes\Workflow\EstimateFormDynamizer::class
            ],
            're-estimate-valuation-sold' => [
                'from' => ['valuation-sold'],
                'to' => 'valuation-sold',
                'formDynamizer' => \Kiiz\Estimator\Classes\Workflow\EstimateFormDynamizer::class
            ],
            'manual-estimate' => [
                'from' => ['pending', 'to-dun'],
                'to' => 'estimated',
                'formFields' => 'kiiz/estimator/classes/workflow/set-price.yaml'
            ],
            'send-contract' => [ // full service only
                'from' => ['estimated'],
                'to' => 'contract-sent',
                'populate' => ['price'],
                'formFields' => 'kiiz/estimator/models/estimate/fields_send_contract.yaml'
            ],
            'validate' => [
                'from' => ['contract-sent', 'rejected'],
                'to' => 'validated',
                'formFields' => 'kiiz/estimator/classes/workflow/set-selling-price.yaml',
                'populate' => ['selling_price']
            ],
            'sell-valuation' => [ // valuation only
                'from' => ['estimated'],
                'to' => 'valuation-sold',
                'populate' => ['price'],
                'formFields' => 'kiiz/estimator/classes/workflow/set-price.yaml',
            ],
            'refresh-progress' => [
                'from' => ['progress', 'error'],
                'to' => 'estimated'
            ],
            'reject' => [
                'from' => ['awaiting-client',
                    'pending',
                    'to-dun',
                    'estimated',
                    'contract-sent',
                    'progress',
                    'error',
                    'validated',
                    'valuation-sold'],
                'to' => 'rejected',
                'formFields' => 'kiiz/estimator/classes/workflow/reject.yaml',
                'redirect' => 'kiiz/estimator/estimates'
            ],
            'error' => [
                'from' => 'progress',
                'to' => 'error'
            ]
        ],
    ]
];
