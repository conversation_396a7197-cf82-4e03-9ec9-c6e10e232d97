<?php
/**
 * Chartjs Line options
 * @see https://www.chartjs.org/docs/latest/
 */
return [
    'line' => [
        /* Chart global options */
        'chartOptions' => [
            'legend' => [
                'display' => true,
            ],
            'tooltips' => [
                'callbacks' => [
                    'label' => 'tooltipItem => `${tooltipItem.yLabel}`',
                    'title' => 'tooltipItems => `${tooltipItems[0].xLabel}`',
                ]
            ],
            'maintainAspectRatio' => false,
            'scales' => [
                'yAxes' => [
                    [
                        'ticks' => [
                            'beginAtZero' => true,
                            'precision' => 1
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'fontSize' => 13
                        ]
                    ]
                ],
                'xAxes' => [
                    [
                        'scaleLabel' => [
                            'display' => true,
                            'fontSize' => 13
                        ]
                    ]
                ]
            ],
        ],
        /* Dataset base options */
        'datasetsOptions' => [
            'fill' => false,
            'borderWidth' => 2,
            'backgroundColor' => '#37003e',
            'borderColor' => '#37003e',
            'pointBackgroundColor' => '#37003e',
            'pointBorderColor' => '#37003e',
            'pointRadius' => 2,
            'pointHitRadius' => 7,
        ]
    ]
];
