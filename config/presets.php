<?php
/**
 * Presets configuration depending on the status of the Model
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018 Kiiz SA
 */
return [
    'estimator' => [
        'estimator:full-service-datareview' => [
            'label' => 'kiiz.estimator::lang.preset.button_estimate_review',
            'status' => [
                \Kiiz\Estimator\Models\Estimate::STATUS_PENDING,
                \Kiiz\Estimator\Models\Estimate::STATUS_ESTIMATED
            ],
            'group_id' => \Kiiz\User\Models\UserGroup::GROUP_FULL_ID
        ],
        'estimator:valuation-datareview' => [
            'label' => 'kiiz.estimator::lang.preset.button_estimate_review',
            'status' => [
                \Kiiz\Estimator\Models\Estimate::STATUS_PENDING,
                \Kiiz\Estimator\Models\Estimate::STATUS_ESTIMATED
            ],
            'group_id' => \Kiiz\User\Models\UserGroup::GROUP_VALUATION_ID
        ],
        /*'estimator:quality-price' => [
            'label' => 'kiiz.estimator::lang.preset.button_quality_price',
            'status' => [
                \Kiiz\Estimator\Models\Estimate::STATUS_VALIDATED,
                \Kiiz\Estimator\Models\Estimate::STATUS_VALUATION_SOLD
            ]
        ],*/
        'estimator:send-contract' => [
            'label' => 'kiiz.estimator::lang.preset.button_send_contract',
            'status' => [
                \Kiiz\Estimator\Models\Estimate::STATUS_ESTIMATED,
                \Kiiz\Estimator\Models\Estimate::STATUS_CONTRACT_SENT
            ],
            'group_id' => \Kiiz\User\Models\UserGroup::GROUP_FULL_ID
        ],
    ],
    'property' => [
        // send publication links to the owner
        'property:publication' => [
            'label' => 'kiiz.property::lang.action.send_publication',
            'status' => [
                \Kiiz\Property\Models\Property::STATUS_PUBLISHED
            ]
        ],
        // send property page for validation by the owner
        'property:owner-validation' => [
            'label' => 'kiiz.property::lang.action.send_validation',
            'status' => [
                \Kiiz\Property\Models\Property::STATUS_WRITING_CONTENT,
                \Kiiz\Property\Models\Property::STATUS_VALIDATION,
            ]
        ]
    ]
];
