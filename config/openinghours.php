<?php
/**
 * Opening Hours
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018 Kiiz SA
 * @see https://github.com/spatie/opening-hours
 */
return [
    'human' => [
        'monday' => ['08:00-18:00'],
        'tuesday' => ['08:00-18:00'],
        'wednesday' => ['08:00-18:00'],
        'thursday' => ['08:00-18:00'],
        'friday' => ['08:00-18:00'],
        'saturday' => [],
        'sunday' => [],
        'exceptions' => [
            '12-25' => [],
            '01-01' => [], // Recurring on each 1st of january
            '08-01' => [], // Recurring on each 1st of august
        ]
    ],
    'robot' => [
        'monday' => ['08:30-19:00'],
        'tuesday' => ['08:30-19:00'],
        'wednesday' => ['08:30-19:00'],
        'thursday' => ['08:30-19:00'],
        'friday' => ['08:30-19:00'],
        'saturday' => ['08:30-19:00'],
        'sunday' => ['08:30-19:00']
    ],
    // opening hours for the property owner visits availability, we have to add an extra minute to the closing hour
    'visit' => [
        'monday' => ['09:00-17:31'],
        'tuesday' => ['09:00-17:31'],
        'wednesday' => ['09:00-17:31'],
        'thursday' => ['09:00-17:31'],
        'friday' => ['09:00-17:31'],
        'saturday' => ['09:00-17:31'],
        'sunday' => [],
        'exceptions' => [
            '12-25' => [],
            '01-01' => [], // Recurring on each 1st of january
            '08-01' => [], // Recurring on each 1st of august
        ]
    ],
    // opening hours for the owners handling themselves their visits
    'visit_solo' => [
        'monday' => ['09:00-19:01'],
        'tuesday' => ['09:00-19:01'],
        'wednesday' => ['09:00-19:01'],
        'thursday' => ['09:00-19:01'],
        'friday' => ['09:00-19:01'],
        'saturday' => ['09:00-19:01'],
        'sunday' => ['09:00-19:01'],
        'exceptions' => [
            '12-25' => [],
            '01-01' => [], // Recurring on each 1st of january
            '08-01' => [], // Recurring on each 1st of august
        ]
    ]
];
