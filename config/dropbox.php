<?php
/**
 * Dropbox configuration
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018 Kiiz SA
 * @link https://www.dropbox.com/developers/documentation/http/documentation
 * @link https://www.dropbox.com/developers/reference/namespace-guide
 * @link https://dropbox.github.io/dropbox-api-v2-explorer
 */
return [
    'enabled' => env('DROPBOX_ENABLE', 'false'),
    'access_token' =>env('DROPBOX_ACCESS_TOKEN', ''),
    'refresh_token' => env('DROPBOX_REFRESH_TOKEN', ''),
    'client_id' => env('DROPBOX_CLIENT_ID', ''),
    'client_secret' => env('DROPBOX_CLIENT_SECRET', ''),
    'root_shared_folder_id' => env('DROPBOX_SHARED_FOLDER_ID', 3825376608),
    'selling_documents_path' => env('DROPBOX_SELLING_DOCUMENTS_PATH', '/TEMP/PREPROD/FOR SALE/'),
    'property_documents_limit' => 100,
    /*
     * the e-mail attachments will be injected in the below directory, located under the client's root document. This
     * directory is not public, but used to manually filter the documents that should be public
     * (see 'public_path' option)
     **/
    'injection_path' => 'Mail_Attachments',
    /*
     * The path of public documents that can be accessed by the buyers
     */
    'public_path' => 'Property_Documents',
    /*
     * The path of the photos that are uploaded in the backend
     */
    'photos_path' => 'Property_Photos',

    /*
     * The path of the documents that will be available to the notary
     */
    'notary_path' => 'Notary_Documents',
];
