<?php

return [

  /*
  |--------------------------------------------------------------------------
  | Third Party Services
  |--------------------------------------------------------------------------
  |
  | This file is for storing the credentials for third party services such
  | as Stripe, Mailgun, Mandrill, and others. This file provides a sane
  | default location for this type of information, allowing packages
  | to have a conventional place to find your various credentials.
  |
  */

  'mailgun' => [
    'domain' => env('MAILGUN_DOMAIN'),
    'secret' => env('MAILGUN_SECRET'),
    'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
  ],

  'mandrill' => [
    'secret' => env('MANDRILL_SECRET'),
  ],

  'ses' => [
    'key' => env('AWS_ACCESS_KEY_ID'),
    'secret' => env('AWS_SECRET_ACCESS_KEY'),
    'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
  ],

  'sparkpost' => [
    'secret' => env('SPARKPOST_SECRET'),
  ],

  'stripe' => [
    'model'  => 'User',
    'secret' => '',
  ],

  /*
    |--------------------------------------------------------------------------
    | Twilio - Test configuration - No SMS are sent
    | (Deprecated because SMS are sent through Frontapp)
    |--------------------------------------------------------------------------
    |
    | Our service provider for phone communication with our users
    | @see https://www.twilio.com/docs/
    | @see https://www.twilio.com/docs/iam/test-credentials#test-sms-messages
    |
    */
    'twilio' => [
        'TWILIO_ACCOUNT_SID' => env('TWILIO_ACCOUNT_SID', '**********************************'),
        'TWILIO_AUTH_TOKEN' => env('TWILIO_AUTH_TOKEN', 'eb63dad85ac9c4151cb34efe637f7c5c'),
        'TWILIO_SENDER_ID' => env('TWILIO_SENDER_ID', '+***********') // Magic number given by Twilio
    ],

  /*
  |--------------------------------------------------------------------------
  | Authy
  | (Deprecated)
  |--------------------------------------------------------------------------
  |
  | Our service provider for phone verification with our users
  | @see https://www.twilio.com/docs/
  | @see https://www.twilio.com/docs/authy/
  |
  */
  'authy' => [
    'AUTHY_API_KEY' => 'fx5iDwBaTuCZhu7QtD88RB409ZC8XcJ6',
    'code_length' => 5
  ],

  'recaptcha' => [
    'site_key' => '6LdivFUUAAAAADdg3o08EY0qZaA7RI6kkZRkVzJW'
  ]

];
