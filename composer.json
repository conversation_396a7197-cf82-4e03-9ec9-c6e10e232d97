{"name": "october/october", "description": "Built using October CMS: The Laravel-Based CMS Engineered For Simplicity", "type": "project", "homepage": "https://octobercms.com", "license": "proprietary", "require": {"php": ">=8.1", "october/rain": "^3.5", "laravel/framework": "^9.0", "october/all": "^3.0", "propaganistas/laravel-phone": "^5.0", "rainlab/translate-plugin": "^2.2", "barryvdh/laravel-dompdf": "^2.0", "intervention/image": "^2.7", "jenssegers/agent": "^2.6", "guzzlehttp/guzzle": "^7.8", "rainlab/user-plugin": "^2.1", "cweagans/composer-patches": "^1.7", "league/fractal": "^0.20.1", "rainlab/blog-plugin": "^1.7", "markrogoyski/math-php": "^2.8", "kunalvarma05/dropbox-php-sdk": "^0.4.2", "spatie/opening-hours": "^2.41", "wikimedia/composer-merge-plugin": "^2.1", "responsiv/uploader": "master", "bennothommo/meta": "master", "spatie/laravel-navigation": "^1.2", "zerodahero/laravel-workflow": "^4.2", "blakejones/magicforms-plugin": "^1.6", "inetis/richeditorsnippets-plugin": "^2.1", "graham-campbell/manager": "^4.7", "illuminate/contracts": "^5.5 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/support": "^5.5 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "league/flysystem": "^3.18.0", "league/flysystem-aws-s3-v3": "^3.16.0", "league/flysystem-azure-blob-storage": "^3.16.0", "spatie/flysystem-dropbox": "^3.0.1", "league/flysystem-webdav": "^3.16.0", "league/flysystem-ziparchive": "^3.16.0", "league/flysystem-ftp": "^3.16.0", "predis/predis": "^2.2", "spatie/laravel-sitemap": "^6.4", "sprain/swiss-qr-bill": "^4.10", "ext-dom": "*"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0", "rainlab/builder-plugin": "^2.0"}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate --ansi"], "post-autoload-dump": ["System\\Console\\ComposerScript::postAutoloadDump"], "post-update-cmd": ["System\\Console\\ComposerScript::postUpdateCmd"], "pre-package-uninstall": ["System\\Console\\ComposerScript::prePackageUninstall"], "test": ["phpunit --stop-on-failure"]}, "config": {"preferred-install": "dist", "allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "wikimedia/composer-merge-plugin": true}, "platform": {"php": "8.1"}}, "autoload": {"psr-4": {"System\\Console\\": "modules/system/console"}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": {"octobercms": {"type": "composer", "url": "https://gateway.octobercms.com"}, "uploader": {"type": "package", "package": {"name": "responsiv/uploader", "version": "master", "type": "october-plugin", "source": {"url": "https://github.com/responsiv/uploader-plugin.git", "type": "git", "reference": "master"}}}, "meta": {"type": "package", "package": {"name": "bennothommo/meta", "version": "master", "type": "october-plugin", "source": {"url": "https://github.com/bennothommo/october-plugin-meta.git", "type": "git", "reference": "master"}}}}, "extra": {"composer-exit-on-patch-failure": true, "patches": {"bennothommo/meta": {"The string \"!!! Initial version of the plugin\" could not be parsed": "plugins/kiiz/patches/bennothommo/meta/unsupported_chars.patch"}, "october/rain": {"Wrong filepath on getThumb()": "plugins/kiiz/patches/october/rain/wrong_filepath_on_getthumb.patch"}}}}