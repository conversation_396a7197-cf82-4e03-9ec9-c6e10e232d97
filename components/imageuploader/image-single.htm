{% set file = __SELF__.singleFile %}

<div
    class="responsiv-uploader-fileupload style-image-single {{ __SELF__.isPopulated() ? 'is-populated' }}"
    data-control="fileupload"
    data-template="#uploaderTemplate{{ __SELF__ }}"
    data-unique-id="{{ __SELF__ }}"
    data-upload-handler="{{ __SELF__ }}::onUpload"
    data-thumbnail-width="{{ __SELF__.imageWidth ?: '0' }}"
    data-thumbnail-height="{{ __SELF__.imageHeight ?: '0' }}"
    {% if __SELF__.fileTypes %}data-file-types="{{ __SELF__.fileTypes }}"{% endif %}
>

    <!-- Field placeholder -->
    <input type="hidden" name="_uploader[{{ __SELF__.attribute }}]" value="" />

    <!-- Add New Image -->
    <a
        href="javascript:;"
        style="{{ __SELF__.getCssBlockDimensions }}"
        class="upload-button">
        <span class="upload-button-icon"></span>
    </a>

    <!-- Existing file -->
    <div class="upload-files-container">
        {% if file %}
            <div class="upload-object is-success" data-id="{{ file.id }}" data-path="{{ file.pathUrl }}">
                <div class="icon-container image">
                    <img src="{{ file.thumbUrl }}" alt="" />
                </div>
                <div class="info">
                    <h4 class="filename">
                        <span data-dz-name>{{ file.title ?: file.file_name }}</span>
                        <a
                            href="javascript:;"
                            class="upload-remove-button"
                            data-request="{{ __SELF__ ~ '::onRemoveAttachment' }}"
                            data-request-confirm="{{ 'responsiv.uploader::lang.are_you_sure' | trans }}"
                            data-request-data="file_id: {{ file.id }}"
                            >&times;</a>
                    </h4>
                    <p class="size">{{ file.sizeToString }}</p>
                </div>
                <div class="meta"></div>
            </div>
        {% endif %}
    </div>

</div>

<!-- Template for new file -->
<script type="text/template" id="uploaderTemplate{{ __SELF__ }}">
    <div class="upload-object dz-preview dz-file-preview">
        <div class="icon-container image">
            <img data-dz-thumbnail style="{{ __SELF__.getCssDimensions }}" />
        </div>
        <div class="info">
            <h4 class="filename">
                <span data-dz-name></span>
                <a
                    href="javascript:;"
                    class="upload-remove-button"
                    data-request="{{ __SELF__ ~ '::onRemoveAttachment' }}"
                    data-request-confirm="{{ 'responsiv.uploader::lang.are_you_sure' | trans }}"
                    >&times;</a>
            </h4>
            <p class="size" data-dz-size></p>
            <p class="error"><span data-dz-errormessage></span></p>
        </div>
        <div class="meta">
            <div class="progress-bar"><span class="upload-progress" data-dz-uploadprogress></span></div>
        </div>
    </div>
</script>
