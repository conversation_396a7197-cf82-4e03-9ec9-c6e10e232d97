//
// Single File
//

.responsiv-uploader-fileupload.style-file-single {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    position: relative;
    padding-right: 11px;

    .upload-button {
        .uploader-vertical-align();
        right: 0;
        margin-right: 0;
    }

    .upload-empty-message {
        padding: 10px 0 10px 11px;
        font-size: 13px;
    }

    &.is-populated {
        .upload-button,
        .upload-empty-message {
            display: none;
        }
    }

    .upload-object {
        display: block;
        width: 100%;
        padding: 8px 0 10px 0;

        .icon-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 35px;
            padding: 0 5px;
            margin: 8px 0 0 7px;
            text-align: center;
        }

        .info {
            margin-left: 54px;
            margin-right: 15%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            h4, p {
                display: inline;
                margin: 0;
                padding: 0;
                font-size: 12px;
                line-height: 150%;
                color: #666666;
            }

            p.size {
                font-weight: normal;
                &:before {
                    content: " - ";
                }
            }

            p.error {
                color: #ab2a1c;
                padding-top: 0;
            }
        }

        .progress-bar {
            .uploader-progress-bar();
            position: absolute;
            top: 50%;
            margin-top: -2px;
            right: 5px;
        }

        .meta {
            .uploader-vertical-align();
            right: 0;
            width: 15%;
            .upload-remove-button {
                position: absolute;
                top: 50%;
                right: 0;
                height: 20px;
                line-height: 20px;
                margin-top: -10px;
                margin-right: 10px;
                z-index: 100;
            }
        }

        .icon-container:after {
            .uploader-small-loader();
        }

        &.is-error {
            .info {
                p.size { display: none; }
                p.error:before {
                    content: " - ";
                }
            }
        }
    }

}