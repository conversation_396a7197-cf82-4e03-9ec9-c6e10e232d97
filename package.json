{"name": "octobercms", "description": "October CMS is a self-hosted CMS platform based on the Laravel PHP Framework.", "directories": {"test": "tests/js/cases", "helpers": "tests/js/helpers"}, "scripts": {"compile-less": "php artisan october:util compile less", "compile-js": "php artisan october:util compile js", "watch-less": "onchange -v \"modules/**/*.less\" \"plugins/**/*.less\" -- php artisan october:util compile less", "watch-js": "onchange -v \"modules/**/*.js\" \"plugins/**/*.js\" --exclude \"**/*-min.js\" -- php artisan october:util compile js", "test": "mocha --require @babel/register tests/js/cases/**/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/octobercms/october.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/octobercms/october/issues"}, "homepage": "https://octobercms.com/", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/register": "^7.5.5", "babel-plugin-module-resolver": "^3.2.0", "chai": "^4.2.0", "jquery": "^3.4.1", "jsdom": "^15.1.1", "mocha": "^6.2.0", "onchange": "^6.1.0", "sinon": "^7.4.1"}}