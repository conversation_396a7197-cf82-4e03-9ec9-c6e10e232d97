version: "5"

services:
  webserver:
    build:
      context: ./docker/bin/webserver
    container_name: 'octoberv3-webserver'
    restart: 'no'
    ports:
      - "8285:80"
      - "9295:443"
    links:
      - mariadb
      - maildev
    volumes:
      - ${DOCUMENT_ROOT-./}:/var/www/html
      - ${PHP_INI-./docker/config/php/docker-php-octobercms-settings.ini}:/usr/local/etc/php/conf.d/docker-php-octobercms-settings.ini
  #      - ${VHOSTS_DIR-./docker/config/vhosts}:/etc/apache2/sites-enabled
  #      - ${LOG_DIR-./docker/logs/apache2}:/var/log/apache2
  mariadb:
    build: ./docker/bin/mysql
    container_name: 'octoberv3-mysql'
    restart: 'no'
    ports:
      - "3215:3306"
    volumes:
        - "mysql_data:/var/lib/mysql:rw"
        - ./db_dumps:/var/mysql/db_dumps
    environment:
      MYSQL_ROOT_PASSWORD: Welcome123
      MYSQL_DATABASE: kiiz
      MYSQL_USER: kiiz
      MYSQL_PASSWORD: kiiz
    command:
        - "--max_allowed_packet=128M"
        - "--innodb_use_native_aio=0"
  redis:
    container_name: 'octoberv3-redis'
    image: redis:latest
    ports:
      - "6275:6379"
    restart: 'no'
  maildev:
    container_name: 'octoberv3-mail'
    image: djfarrelly/maildev
    ports:
      - 7275:80
    restart: 'no'

volumes:
    mysql_data:
        driver: local
