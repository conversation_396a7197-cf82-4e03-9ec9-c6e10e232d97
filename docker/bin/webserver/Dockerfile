FROM php:8.2-apache-bullseye

RUN apt-get -y update --fix-missing
RUN apt-get upgrade -y

# Install important libraries
RUN apt-get -y install --fix-missing apt-utils build-essential git curl libcurl4 libcurl3-dev zip libxml2-dev

# Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install xdebug
RUN pecl install xdebug-3.2.0
RUN docker-php-ext-enable xdebug

# Other PHP7 Extensions

RUN apt-get -y install libmcrypt-dev
RUN pecl install mcrypt-1.0.6
RUN docker-php-ext-enable mcrypt

RUN apt-get -y install libsqlite3-dev libsqlite3-0 default-mysql-client
RUN docker-php-ext-install pdo_mysql
RUN docker-php-ext-install pdo_sqlite
RUN docker-php-ext-install mysqli

R<PERSON> docker-php-ext-install curl
RUN docker-php-ext-install soap

RUN apt-get -y install zlib1g-dev libzip-dev
RUN docker-php-ext-install zip

RUN apt-get -y install libicu-dev
RUN docker-php-ext-install -j$(nproc) intl

RUN apt-get install -y libfreetype6-dev libjpeg62-turbo-dev libpng-dev libwebp-dev
RUN docker-php-ext-configure gd --with-freetype=/usr/include/ --with-jpeg=/usr/include/ --with-webp
RUN docker-php-ext-install -j$(nproc) gd

RUN apt-get -y install exiftool
RUN docker-php-ext-install exif

RUN apt-get update && apt-get install -y --no-install-recommends libssl-dev \
    && docker-php-ext-configure ftp --with-openssl-dir=/usr \
    && docker-php-ext-install ftp

# Enable apache modules
RUN a2enmod rewrite headers

# Installing locales
RUN apt-get install -y locales
RUN sed -i 's/# en_GB.UTF-8 UTF-8/en_GB.UTF-8 UTF-8/' /etc/locale.gen \
    && sed -i 's/# fr_CH.UTF-8 UTF-8/fr_CH.UTF-8 UTF-8/' /etc/locale.gen \
    && sed -i 's/# de_CH.UTF-8 UTF-8/de_CH.UTF-8 UTF-8/' /etc/locale.gen \
    && sed -i 's/# it_CH.UTF-8 UTF-8/it_CH.UTF-8 UTF-8/' /etc/locale.gen \
    && sed -i 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen \
    && locale-gen
