/bootstrap/compiled.php
/vendor
composer.phar
.DS_Store
.idea
.env
.env.*.php
.env.php
php_errors.log
nginx-error.log
nginx-access.log
nginx-ssl.access.log
nginx-ssl.error.log
php-errors.log
sftp-config.json
selenium.php
robots.txt
.htaccess
.htpasswd
.phpstorm.meta.php
_ide_helper.php

# for netbeans
nbproject

# ------------------------------------------------------------------------------
# .gitignore for OctoberCMS (http://octobercms.com/)
# Based on https://gist.github.com/salcode/b515f520d3f8207ecd04 for WordPress
# ------------------------------------------------------------------------------
# By default all files are ignored.
#
# At the end of the file you will need to whitelist themes and plugins you want
# to include in the repo.
#
# Update the {PLACEHOLDERS} to reflect your setup.
# ------------------------------------------------------------------------------

# October-specific rules
# ----------------------------------------------------------

# Ignore everything in the root except the "plugins" and "themes" directories

!themes/

# Ignore everything in the "themes" directory
themes/*

# Ignore core modules
modules/*

# Ignore everything in the "plugins" directory, except Kiiz namespace
plugins/*
!plugins/kiiz/

# Ignore PEM certificates
plugins/kiiz/**/*.pem

# If you have a mixture of private (in repo) and public (published to market)
# plugins, uncomment the following line to ignore all plugins in your namespace
# because you'll want to manage public plugins via the automatic updater
#plugins/kiiz/*

# Miscellaneous rules
# ----------------------------------------------------------

# Ignore node depenencies
node_modules/

# Ignore log files and databases
*.log
*.sqlite

# Ignore packaged files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*error.csv

# Ignore temporary files
.~*

# Whitelist rules
# ----------------------------------------------------------

# Misc
!.gitignore
!.editorconfig
!composer.json
!package.json

# Themes
!themes/kiiz/
!themes/kiiz-microsite/

# Plugins
# If all of your plugins are private and you didn't uncomment line 31,
# then you don't need these rules
!plugins/kiiz/private-plugin-one/
!plugins/kiiz/private-plugin-two/

# Ignoring database configuration, this must be set by hand on the server
config/prod/database.php
config/prod/mail.php
config/prod/dropbox.php
config/prod/api.php
config/preprod/database.php
config/preprod/mail.php
config/prod/dropbox.php
config/testing/database.php
config/dev/kiizprice.php
config/dev/dropbox.php
config/dev/queue.php
config/dev/database.php
config/preprod/kiizprice.php
config/prod/kiizprice.php

#Google search console
googlea0977bc06dd67676.html

# ignoring package-lock.json
themes/kiiz/package-lock.json
plugins/kiiz/backend/assets/package-lock.json

#ignore credentials to october gateway
auth.json
