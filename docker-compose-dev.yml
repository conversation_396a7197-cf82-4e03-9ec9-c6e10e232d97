version: "3"

services:
  webserver:
    build:
      context: ./docker/bin/webserver
    container_name: 'kiiz-webserver'
    restart: 'no'
    ports:
      - "8484:80"
      - "9494:443"
    links:
      - mysql
      - maildev
    volumes:
      - ${DOCUMENT_ROOT-./}:/var/www/html
      - ${PHP_INI-./docker/config/php/php.ini}:/usr/local/etc/php/php.ini
      - ${VHOSTS_DIR-./docker/config/vhosts}:/etc/apache2/sites-enabled
      - ${LOG_DIR-./docker/logs/apache2}:/var/log/apache2
    networks:
      default:
        aliases:
          - "www.kiiz.local"
          - "dev.kiiz.local"

  mysql:
    build: ./docker/bin/mysql
    container_name: 'kiiz-mysql'
    restart: 'no'
    ports:
      - "3314:3306"
    volumes:
      - ${MYSQL_DATA_DIR-./docker/data/mysql}:/var/lib/mysql
      - ${MYSQL_LOG_DIR-./docker/logs/mysql}:/var/log/mysql/home/<USER>
    environment:
      MYSQL_ROOT_PASSWORD: Welcome123
      MYSQL_DATABASE: kiiz
      MYSQL_USER: kiiz
      MYSQL_PASSWORD: kiiz
    command: --innodb-use-native-aio="0" --sql_mode="IGNORE_SPACE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"

  redis:
    container_name: 'kiiz-redis'
    image: redis:latest
    ports:
      - "6379:6379"
    restart: 'no'

  maildev:
    container_name: 'kiiz-mail'
    image: djfarrelly/maildev
    ports:
      - 7272:80
    restart: 'no'
